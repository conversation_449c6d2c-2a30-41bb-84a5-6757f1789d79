const express = require('express');
const sgMail = require('@sendgrid/mail');
const bodyParser = require('body-parser');
const cors = require('cors');
require('dotenv').config();
const path = require('path');
const initializeFirebase = require('./config/firebase.config');
const zohoCRM = require('./integrations/zoho-crm');

// Determine which Stripe keys to use based on STRIPE_MODE
const stripeMode = process.env.STRIPE_MODE || 'test';
console.log(`Using Stripe in ${stripeMode.toUpperCase()} mode`);

// Initialize Stripe with the appropriate key
const stripeSecretKey = stripeMode === 'live'
  ? process.env.STRIPE_LIVE_SECRET_KEY
  : process.env.STRIPE_TEST_SECRET_KEY;
const stripe = require('stripe')(stripeSecretKey);

// Initialize Firebase
const admin = initializeFirebase();

const app = express();

// Function to calculate subscription end date
function calculateSubscriptionEndDate({ isYearlySubscription, startDate, currentEndDate, stripeCurrentPeriodEnd, isRenewal, logPrefix = '' }) {
  // For renewals, we extend from the current end date if available
  if (isRenewal && currentEndDate) {
    // For yearly subscriptions, add exactly 1 year to the current end date
    if (isYearlySubscription) {
      const newEndDate = new Date(
        currentEndDate.getFullYear() + 1,
        currentEndDate.getMonth(),
        currentEndDate.getDate(),
        23, 59, 59, 999
      );
      console.log(`${logPrefix} Renewal: extending yearly subscription by 1 year to ${newEndDate.toISOString()}`);
      return newEndDate;
    }
    // For monthly subscriptions, add 1 month to the current end date
    else {
      const newEndDate = new Date(currentEndDate);
      newEndDate.setMonth(newEndDate.getMonth() + 1);
      console.log(`${logPrefix} Renewal: extending monthly subscription by 1 month to ${newEndDate.toISOString()}`);
      return newEndDate;
    }
  }

  // For new subscriptions or when current end date is not available
  // For yearly subscriptions, calculate 1 year from start date
  if (isYearlySubscription && startDate) {
    const endDate = new Date(
      startDate.getFullYear() + 1,
      startDate.getMonth(),
      startDate.getDate(),
      23, 59, 59, 999
    );
    console.log(`${logPrefix} New subscription: setting yearly end date to ${endDate.toISOString()}`);
    return endDate;
  }

  // For monthly subscriptions or when start date is not available, use Stripe's current_period_end
  if (stripeCurrentPeriodEnd) {
    const endDate = new Date(stripeCurrentPeriodEnd * 1000);
    console.log(`${logPrefix} Using Stripe's current_period_end: ${endDate.toISOString()}`);
    return endDate;
  }

  // Fallback: use current date + 30 days for monthly, or current date + 1 year for yearly
  const now = new Date();
  if (isYearlySubscription) {
    const fallbackEndDate = new Date(
      now.getFullYear() + 1,
      now.getMonth(),
      now.getDate(),
      23, 59, 59, 999
    );
    console.log(`${logPrefix} Fallback: setting yearly end date to ${fallbackEndDate.toISOString()}`);
    return fallbackEndDate;
  } else {
    const fallbackEndDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    console.log(`${logPrefix} Fallback: setting monthly end date to ${fallbackEndDate.toISOString()}`);
    return fallbackEndDate;
  }
}

// Function to award credits and update subscription details
async function awardCreditsAndUpdateSubscription({
  userId,
  creditsToAdd,
  currentCredits,
  planName,
  endDate,
  subscriptionId = null,
  isYearlySubscription = true,
  isRenewal = false,
  logPrefix = '',
  subscriptionHistory = [],
  additionalData = {}
}) {
  try {
    console.log(`${logPrefix} Awarding ${creditsToAdd} credits to user ${userId} for plan ${planName}`);
    console.log(`${logPrefix} Current credits: ${currentCredits}, New total: ${currentCredits + creditsToAdd}`);
    console.log(`${logPrefix} Subscription end date: ${endDate.toISOString()}`);
    console.log(`${logPrefix} Is renewal: ${isRenewal}, Is yearly: ${isYearlySubscription}`);

    // Prepare the update data
    const updateData = {
      credits: currentCredits + creditsToAdd,
      subscriptionType: planName,
      subscriptionActive: true,
      paid: true,
      subscriptionEndDate: admin.firestore.Timestamp.fromDate(endDate),
      subscriptionRenewalDate: admin.firestore.Timestamp.fromDate(endDate),
      subscriptionInterval: isYearlySubscription ? 'yearly' : 'monthly',
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    // Add subscription ID if provided
    if (subscriptionId) {
      updateData.subscriptionId = subscriptionId;
    }

    // Add subscription history if provided
    if (subscriptionHistory && subscriptionHistory.length > 0) {
      updateData.subscriptionHistory = subscriptionHistory;
    }

    // Add any additional data
    if (additionalData && Object.keys(additionalData).length > 0) {
      Object.assign(updateData, additionalData);
    }

    // Update the user's document in Firestore
    await admin.firestore().collection('Admins').doc(userId).update(updateData);

    console.log(`${logPrefix} Successfully updated user ${userId} with ${creditsToAdd} credits for plan ${planName}`);

    return {
      success: true,
      message: `Successfully updated user ${userId} with ${creditsToAdd} credits for plan ${planName}`
    };
  } catch (error) {
    console.error(`${logPrefix} Error updating subscription for user ${userId}:`, error);
    return {
      success: false,
      error: error.message || 'Unknown error updating subscription'
    };
  }
}

// Helper function to get plan details from a price ID
function getPlanDetailsFromPriceId(priceId) {
  // Test mode price IDs
  const testPriceMap = {
    // Test subscription price IDs
    'price_1RNpW7PqOZsaOO5knOoIbBb0': { name: 'Assess1', credits: 1 },
    'price_1RMP2WPqOZsaOO5kEXSQnBS0': { name: 'Assess100', credits: 100 },
    'price_1RMP2ZPqOZsaOO5kpAcmM2Ur': { name: 'Assess250', credits: 250 },
    'price_1RMP2cPqOZsaOO5kOzAGzqc7': { name: 'Assess500', credits: 500 },
    // Test top-up price IDs
    'price_1RMP2fPqOZsaOO5kk3cePRnn': { name: 'TopUp100', credits: 100 },
    'price_1RMP2hPqOZsaOO5k8YEHnxu4': { name: 'TopUp250', credits: 250 },
    'price_1RMP2kPqOZsaOO5kRLmLuPwu': { name: 'TopUp500', credits: 500 },
    // Free trial
    'price_1REmsePqOZsaOO5kgjo38qgp': { name: 'Free Trial', credits: 5 }
  };

  // Live mode price IDs
  const livePriceMap = {
    // Live subscription price IDs
    'price_1RNo7DL8F65CEkir2BjRHcAh': { name: 'Assess1', credits: 1 },
    'price_1RMjI6L8F65CEkirK8t2V3DG': { name: 'Assess100', credits: 100 },
    'price_1RMjI4L8F65CEkirnaFfwST4': { name: 'Assess250', credits: 250 },
    'price_1RMjI2L8F65CEkirqOJxuD42': { name: 'Assess500', credits: 500 },
    // Live top-up price IDs
    'price_1RMjI0L8F65CEkirmgGw2jDw': { name: 'TopUp100', credits: 100 },
    'price_1RMjHuL8F65CEkirXNMZ4UnJ': { name: 'TopUp250', credits: 250 },
    'price_1RMjHpL8F65CEkirTkzlPtEB': { name: 'TopUp500', credits: 500 }
  };

  // Select the appropriate map based on mode
  const priceMap = stripeMode === 'live' ? livePriceMap : testPriceMap;

  return priceMap[priceId] || null;
}

// Generate Stripe config file with publishable key based on mode
const fs = require('fs');
const stripeConfigPath = path.join(__dirname, 'public', 'stripe-config.js');

// Select the appropriate publishable key based on mode
const stripePublishableKey = stripeMode === 'live'
  ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY
  : process.env.STRIPE_TEST_PUBLISHABLE_KEY;

fs.writeFileSync(
  stripeConfigPath,
  `// This file is generated by the server to expose the Stripe publishable key
// Current mode: ${stripeMode.toUpperCase()}
window.STRIPE_PUBLISHABLE_KEY = '${stripePublishableKey}';
window.STRIPE_MODE = '${stripeMode}';

// Note: The stripe-price-config.js script should be loaded after this file
// to ensure the correct price IDs are used based on the current mode.`
);

// CORS configuration
const corsOptions = {
  origin: [
    'http://localhost:3000',
    'https://assessment-dashboard-oowu.onrender.com',
    'https://dashboard.skillsassess.ai'
  ],
  optionsSuccessStatus: 200,
};

app.use(cors(corsOptions));

sgMail.setApiKey(process.env.SENDGRID_API_KEY);


// Special raw body parser for Stripe webhooks - MUST come before other body parsers
app.use('/webhook', express.raw({ type: 'application/json' }));

// Regular body parsers for other routes
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());

app.use(express.static(path.join(__dirname, 'public')));

const sendMail = async (to, subject, body) => {
  const msg = {
    to,
    from: {
      email: '<EMAIL>',
      name: 'SkillsAssess',
    },
    subject,
    text: body,
  };

  try {
    await sgMail.send(msg);
    console.log('Email sent successfully');
    return 'Email sent successfully';
  } catch (error) {
    console.error('Error sending email:', error);
    if (error.response) {
      console.error(error.response.body);
    }
    throw error;
  }
};

// Route handler for the root path
app.get('/', (_req, res) => {
  res.send('Hello, World!');
});

// Route to send email
app.post('/send-email', async (req, res) => {
  const { to, subject, body } = req.body;

  try {
    const result = await sendMail(to, subject, body);
    res.status(200).json({ message: result });
  } catch (error) {
    res.status(500).json({ error: 'Error sending email' });
  }
});

app.post('/send-invitations', async (req, res) => {
  const invitees = req.body.invitees;
  const userCompany = req.body.userCompany;
  const webLink = req.body.webLink;
  const results = {
    sent: [],
    failed: []
  };

  const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
  };

  for (const invitee of invitees) {
    const msg = {
      to: invitee.email,
      from: {
        email: '<EMAIL>',
        name: 'SkillsAssess',
      },
      templateId: 'd-19c4507f92ab431a93eda643b423cbea',
      dynamic_template_data: {
        firstName: capitalizeFirstLetter(invitee.firstName),
        company: capitalizeFirstLetter(userCompany),
        webLink: webLink
      },
    };

    try {
      await sgMail.send(msg);
      results.sent.push(invitee.email);
      console.log(`Invitation sent successfully to ${invitee.email}`);
    } catch (error) {
      results.failed.push(invitee.email);
      console.error(`Failed to send invitation to ${invitee.email}:`, error);
      if (error.response) {
        console.error(error.response.body);
      }
    }
  }

  res.status(200).json(results);
});

app.post('/send-order-confirmation', async (req, res) => {
  const { userCompany, enrollments, totalCost } = req.body;

  const enrollmentDetails = enrollments.map(enrollment =>
    `${enrollment.name} (${enrollment.email}) - ${enrollment.section}
    Courses: ${enrollment.courses.join(', ')}`
  ).join('\n\n');

  const emailBody = `
Hi Liliana,

You've received an order to enroll ${enrollments.length} user(s) on behalf of ${userCompany} from the assessment dashboard.

Enrollment Details:
${enrollmentDetails}

Total Cost: £${totalCost.toFixed(2)}

Best regards,
SkillsAssess Team
  `;

  const msg = {
    to: '<EMAIL>',
    from: {
      email: '<EMAIL>',
      name: 'SkillsAssess',
    },
    subject: `New Enrollment Order from ${userCompany}`,
    text: emailBody,
  };

  try {
    await sgMail.send(msg);
    console.log('Order confirmation email sent successfully');
    res.status(200).json({ message: 'Order confirmation email sent successfully' });
  } catch (error) {
    console.error('Error sending order confirmation email:', error);
    if (error.response) {
      console.error(error.response.body);
    }
    res.status(500).json({ error: 'Error sending order confirmation email' });
  }
});

app.get('/verify-email', (_req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'verify-email.html'));
});

app.post('/handle-email-change', async (req, res) => {
  const { oldEmail, newEmail, action } = req.body;
  console.log('[Email Change] Received request:', { oldEmail, newEmail, action });

  if (!oldEmail || !newEmail) {
    console.error('[Email Change] Missing required fields');
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const db = admin.firestore();

    switch(action) {
      case 'initiate':
        console.log('[Email Change] Initiating change process');

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(newEmail)) {
          return res.status(400).json({ error: 'Invalid email format' });
        }

        // Check if new email already exists in auth
        try {
          const userRecord = await admin.auth().getUserByEmail(newEmail);
          if (userRecord) {
            return res.status(400).json({ error: 'Email already in use' });
          }
        } catch (error) {
          // Error code auth/user-not-found is expected and means we can proceed
          if (error.code !== 'auth/user-not-found') {
            throw error;
          }
        }

        // Get the old admin document
        const oldAdminDoc = await db.collection('Admins').doc(oldEmail).get();
        if (!oldAdminDoc.exists) {
          return res.status(404).json({ error: 'Original admin document not found' });
        }

        // Get old user auth record
        const oldUserRecord = await admin.auth().getUserByEmail(oldEmail);

        // Start a batch operation
        const batch = db.batch();

        // Delete the old admin document
        batch.delete(db.collection('Admins').doc(oldEmail));

        // Create new admin document
        batch.set(db.collection('Admins').doc(newEmail), {
          ...oldAdminDoc.data(),
          email: newEmail,
          emailVerified: false,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Commit the batch
        await batch.commit();

        // Delete the old auth account
        await admin.auth().deleteUser(oldUserRecord.uid);

        console.log('[Email Change] Initiation completed');
        res.status(200).json({
          message: 'Email change initiated successfully',
          status: 'verification_pending',
          newEmail: newEmail,
          requiresReauth: true,
          nextSteps: [
            'Verify your new email address',
            'Sign in again with your new credentials'
          ]
        });
        break;

      case 'cancel':
        // Optional endpoint to cancel if needed
        console.log('[Email Change] Cancelling change');
        res.status(200).json({ message: 'Email change cancelled' });
        break;

      default:
        console.log('[Email Change] Invalid action received:', action);
        res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('[Email Change] Error:', error);
    res.status(500).json({
      error: 'Failed to process email change',
      details: error.message,
      code: error.code
    });
  }
});

app.post('/delete-account', async (req, res) => {
  const { email, company } = req.body;
  console.log('[Account Deletion] Starting deletion process for:', { email, company });

  try {
    const db = admin.firestore();
    const batch = db.batch();

    // Get user data to check for active subscriptions
    const adminRef = db.collection('Admins').doc(email);
    const adminDoc = await adminRef.get();

    if (adminDoc.exists) {
      const userData = adminDoc.data();

      // Cancel subscription if active
      if (userData.subscriptionId && userData.subscriptionActive) {
        try {
          console.log(`[Account Deletion] Canceling subscription ${userData.subscriptionId} for user ${email}`);

          // Cancel immediately in Stripe
          await stripe.subscriptions.cancel(userData.subscriptionId, {
            invoice_now: false,
            prorate: true
          });

          console.log(`[Account Deletion] Successfully canceled subscription ${userData.subscriptionId}`);
        } catch (subscriptionError) {
          console.error('[Account Deletion] Error canceling subscription:', subscriptionError);
          // Continue with account deletion even if subscription cancellation fails
        }
      }
    }

    // First, get and delete the Firebase Auth user
    try {
      const userRecord = await admin.auth().getUserByEmail(email);
      console.log('[Account Deletion] Found auth user:', userRecord.uid);
      await admin.auth().deleteUser(userRecord.uid);
      console.log('[Account Deletion] Deleted auth user successfully');
    } catch (authError) {
      console.error('[Account Deletion] Error deleting auth user:', authError);
      throw new Error('Failed to delete authentication user');
    }

    // Delete admin document
    console.log('[Account Deletion] Removing admin document');
    batch.delete(adminRef);

    // Delete user data from company collection
    if (company) {
      console.log('[Account Deletion] Removing user data from company:', company);
      const companyUserRef = db.collection('companies')
        .doc(company)
        .collection('users')
        .doc(email);
      batch.delete(companyUserRef);
    }

    // Execute batch operation
    await batch.commit();
    console.log('[Account Deletion] Successfully deleted user data');

    res.status(200).json({
      message: 'Account deleted successfully',
      status: 'success',
      requireSignOut: true
    });
  } catch (error) {
    console.error('[Account Deletion] Error:', error);
    res.status(500).json({
      error: 'Failed to delete account',
      details: error.message
    });
  }
});

// Endpoint to delete company and all associated admin accounts
app.post('/delete-company', async (req, res) => {
  const { companyId, companyName, adminEmails } = req.body;
  console.log('[Company Deletion] Starting deletion process for:', { companyId, companyName, adminEmails });

  if (!companyId || !companyName || !adminEmails || !Array.isArray(adminEmails)) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const db = admin.firestore();
    const batch = db.batch();

    // Delete company document
    const companyRef = db.collection('companies').doc(companyId);
    batch.delete(companyRef);

    // Delete all users in the company
    const usersSnapshot = await companyRef.collection('users').get();
    usersSnapshot.forEach(userDoc => {
      batch.delete(userDoc.ref);
    });

    // Delete admin documents and Firebase Auth accounts
    const authDeletionPromises = [];
    for (const email of adminEmails) {
      // Delete admin document
      const adminRef = db.collection('Admins').doc(email);
      batch.delete(adminRef);

      // Delete Firebase Auth account
      try {
        const userRecord = await admin.auth().getUserByEmail(email);
        authDeletionPromises.push(admin.auth().deleteUser(userRecord.uid));
        console.log(`[Company Deletion] Scheduled auth deletion for: ${email}`);
      } catch (authError) {
        console.error(`[Company Deletion] Error finding auth user ${email}:`, authError);
        // Continue with other deletions even if one fails
      }
    }

    // Execute Firestore batch deletion
    await batch.commit();
    console.log('[Company Deletion] Firestore documents deleted successfully');

    // Execute Firebase Auth deletions
    const authResults = await Promise.allSettled(authDeletionPromises);
    const authFailures = authResults.filter(result => result.status === 'rejected');

    if (authFailures.length > 0) {
      console.error('[Company Deletion] Some auth deletions failed:', authFailures);
    }

    console.log(`[Company Deletion] Successfully deleted company ${companyName} and ${adminEmails.length} admin account(s)`);

    res.status(200).json({
      message: `Company "${companyName}" and associated accounts deleted successfully`,
      deletedAdmins: adminEmails.length,
      authFailures: authFailures.length
    });

  } catch (error) {
    console.error('[Company Deletion] Error during deletion process:', error);
    res.status(500).json({
      error: 'Failed to delete company and associated accounts',
      details: error.message
    });
  }
});

// Endpoint to delete company and all associated admin accounts
app.post('/delete-company', async (req, res) => {
  const { companyId, companyName, adminEmails } = req.body;
  console.log('[Company Deletion] Starting deletion process for:', { companyId, companyName, adminEmails });

  if (!companyId || !companyName || !adminEmails || !Array.isArray(adminEmails)) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const db = admin.firestore();
    const batch = db.batch();

    // Delete company document
    const companyRef = db.collection('companies').doc(companyId);
    batch.delete(companyRef);

    // Delete all users in the company
    const usersSnapshot = await companyRef.collection('users').get();
    usersSnapshot.forEach(userDoc => {
      batch.delete(userDoc.ref);
    });

    // Delete admin documents and Firebase Auth accounts
    const authDeletionPromises = [];
    for (const email of adminEmails) {
      // Delete admin document
      const adminRef = db.collection('Admins').doc(email);
      batch.delete(adminRef);

      // Delete Firebase Auth account
      try {
        const userRecord = await admin.auth().getUserByEmail(email);
        authDeletionPromises.push(admin.auth().deleteUser(userRecord.uid));
        console.log(`[Company Deletion] Scheduled auth deletion for: ${email}`);
      } catch (authError) {
        console.error(`[Company Deletion] Error finding auth user ${email}:`, authError);
        // Continue with other deletions even if one fails
      }
    }

    // Execute Firestore batch deletion
    await batch.commit();
    console.log('[Company Deletion] Firestore documents deleted successfully');

    // Execute Firebase Auth deletions
    const authResults = await Promise.allSettled(authDeletionPromises);
    const authFailures = authResults.filter(result => result.status === 'rejected');

    if (authFailures.length > 0) {
      console.error('[Company Deletion] Some auth deletions failed:', authFailures);
    }

    console.log(`[Company Deletion] Successfully deleted company ${companyName} and ${adminEmails.length} admin account(s)`);

    res.status(200).json({
      message: `Company "${companyName}" and associated accounts deleted successfully`,
      deletedAdmins: adminEmails.length,
      authFailures: authFailures.length
    });

  } catch (error) {
    console.error('[Company Deletion] Error during deletion process:', error);
    res.status(500).json({
      error: 'Failed to delete company and associated accounts',
      details: error.message
    });
  }
});

// Endpoint to reactivate a canceled subscription
app.post('/reactivate-subscription', async (req, res) => {
  const { userId, subscriptionId } = req.body;
  console.log('[Subscription Reactivation] Starting reactivation process for:', { userId, subscriptionId });

  // Add a unique request ID to track this specific reactivation request
  const requestId = `reactivate-${userId}-${Date.now()}`;
  console.log(`[Subscription Reactivation] Request ID: ${requestId}`);

  try {
    // Check if the subscription exists and is valid for reactivation
    console.log(`[Subscription Reactivation] [${requestId}] Retrieving subscription from Stripe`);
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    if (!subscription) {
      console.log(`[Subscription Reactivation] [${requestId}] Subscription not found`);
      return res.status(404).json({ error: 'Subscription not found' });
    }

    console.log(`[Subscription Reactivation] [${requestId}] Subscription status: ${subscription.status}, cancel_at_period_end: ${subscription.cancel_at_period_end}`);

    // Check if the subscription is in a state that can be reactivated
    if (subscription.status !== 'active' && subscription.status !== 'canceled') {
      console.log(`[Subscription Reactivation] [${requestId}] Subscription cannot be reactivated due to status: ${subscription.status}`);
      return res.status(400).json({ error: `Subscription cannot be reactivated (status: ${subscription.status})` });
    }

    // If the subscription is already active but marked for cancellation at period end
    if (subscription.status === 'active' && subscription.cancel_at_period_end) {
      // Get the user's current data to check for existing end date
      console.log(`[Subscription Reactivation] [${requestId}] Retrieving user data from Firestore`);
      const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
      if (!userDoc.exists) {
        console.log(`[Subscription Reactivation] [${requestId}] User not found in Firestore`);
        return res.status(404).json({ error: 'User not found' });
      }

      const userData = userDoc.data();
      console.log(`[Subscription Reactivation] [${requestId}] User data:`, {
        subscriptionType: userData.subscriptionType,
        subscriptionInterval: userData.subscriptionInterval,
        subscriptionEndDate: userData.subscriptionEndDate ?
          (userData.subscriptionEndDate.toDate ? userData.subscriptionEndDate.toDate().toISOString() : userData.subscriptionEndDate) : null
      });

      // Check if the subscription is already reactivated (not marked for cancellation)
      if (!subscription.cancel_at_period_end) {
        console.log(`[Subscription Reactivation] [${requestId}] Subscription is already active and not marked for cancellation`);
        return res.status(200).json({
          message: 'Subscription is already active',
          subscription: subscription
        });
      }

      // Remove the cancellation at period end
      console.log(`[Subscription Reactivation] [${requestId}] Updating subscription in Stripe to remove cancellation`);
      const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false
      });

      console.log(`[Subscription Reactivation] [${requestId}] Successfully reactivated subscription ${subscriptionId}`);

      // Prepare update data - keep the existing end date
      const updateData = {
        subscriptionCancelRequested: false,
        subscriptionCancelRequestDate: null,
        subscriptionActive: true,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      // Track subscription history
      let subscriptionHistory = [];
      if (userData.subscriptionHistory) {
        try {
          subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
        } catch (e) {
          console.error(`[Subscription Reactivation] [${requestId}] Error parsing subscription history:`, e);
          subscriptionHistory = [];
        }
      }

      // Add reactivation entry to history
      subscriptionHistory.push({
        type: userData.subscriptionType,
        action: 'reactivated',
        date: new Date().toISOString(),
        requestId: requestId,
        previousEndDate: userData.subscriptionEndDate ?
          (userData.subscriptionEndDate.toDate ? userData.subscriptionEndDate.toDate().toISOString() : userData.subscriptionEndDate) : null
      });

      updateData.subscriptionHistory = subscriptionHistory;

      // Update the user's document in Firestore
      console.log(`[Subscription Reactivation] [${requestId}] Updating user data in Firestore`);
      await admin.firestore().collection('Admins').doc(userId).update(updateData);
      console.log(`[Subscription Reactivation] [${requestId}] Firestore update completed successfully`);

      return res.status(200).json({
        message: 'Subscription reactivated successfully',
        requestId: requestId,
        subscription: updatedSubscription
      });
    }

    // If the subscription is already fully canceled, we need to create a new one
    if (subscription.status === 'canceled') {
      console.log(`[Subscription Reactivation] [${requestId}] Subscription is already fully canceled`);
      return res.status(400).json({
        error: 'Subscription is already fully canceled. Please create a new subscription.'
      });
    }

    // If we get here, something unexpected happened
    console.log(`[Subscription Reactivation] [${requestId}] Unexpected subscription state`);
    return res.status(400).json({
      error: 'Unable to reactivate subscription. Please contact support.'
    });

  } catch (error) {
    console.error(`[Subscription Reactivation] [${requestId}] Error:`, error);
    res.status(500).json({
      error: 'Failed to reactivate subscription',
      details: error.message,
      requestId: requestId
    });
  }
});

// Endpoint to cancel a subscription
app.post('/cancel-subscription', async (req, res) => {
  const { userId, subscriptionId } = req.body;
  console.log('[Subscription Cancellation] Starting cancellation process for:', { userId, subscriptionId });

  if (!userId || !subscriptionId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    // First, cancel the subscription in Stripe
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true
    });

    console.log(`[Subscription Cancellation] Subscription ${subscriptionId} marked for cancellation at period end`);

    // Log the subscription object for debugging
    console.log('[Subscription Cancellation] Subscription details:', {
      id: subscription.id,
      status: subscription.status,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      start_date: subscription.start_date,
      created: subscription.created,
      cancel_at_period_end: subscription.cancel_at_period_end
    });

    // Get the user's current data to check subscription type and start date
    const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();
    const isYearlySubscription = userData.subscriptionInterval === 'yearly' ||
                                (userData.subscriptionType && userData.subscriptionType.toLowerCase().includes('yearly'));

    console.log(`[Subscription Cancellation] Subscription type: ${userData.subscriptionType}, interval: ${userData.subscriptionInterval}`);

    // Get the cancellation date (end of current period)
    let cancelDate;
    let cancelDateMillis;

    // For yearly subscriptions, use the current end date from Firestore if available, otherwise calculate from start date
    if (isYearlySubscription) {
      // First check if we have a valid subscription end date in Firestore (most reliable method)
      if (userData.subscriptionEndDate) {
        cancelDate = userData.subscriptionEndDate.toDate ?
          userData.subscriptionEndDate.toDate() :
          new Date(userData.subscriptionEndDate);
        cancelDateMillis = cancelDate.getTime();
        console.log(`[Subscription Cancellation] Using existing end date from Firestore: ${cancelDate.toISOString()}`);
      } else {
        // If no end date in Firestore, fall back to calculating from start date
        // Try to get the subscription start date from various sources
        let startDate;

        if (userData.subscriptionStartDate) {
          // Use the start date from Firestore (most reliable)
          startDate = userData.subscriptionStartDate.toDate ?
            userData.subscriptionStartDate.toDate() :
            new Date(userData.subscriptionStartDate);
          console.log(`[Subscription Cancellation] Using start date from Firestore: ${startDate.toISOString()}`);
        } else if (subscription.start_date) {
          // Fallback to Stripe's start date
          startDate = new Date(subscription.start_date * 1000);
          console.log(`[Subscription Cancellation] Using Stripe start_date: ${startDate.toISOString()}`);
        } else if (subscription.created) {
          // Fallback to Stripe's created date
          startDate = new Date(subscription.created * 1000);
          console.log(`[Subscription Cancellation] Using Stripe created date: ${startDate.toISOString()}`);
        } else if (subscription.current_period_start) {
          // Fallback to current period start
          startDate = new Date(subscription.current_period_start * 1000);
          console.log(`[Subscription Cancellation] Using current_period_start: ${startDate.toISOString()}`);
        } else {
          // Last resort: use current date minus 1 day (to avoid immediate cancellation)
          const now = new Date();
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          console.log(`[Subscription Cancellation] No valid start date found, using yesterday: ${startDate.toISOString()}`);
        }

        // Calculate end date as exactly 1 year from start date
        cancelDate = new Date(
          startDate.getFullYear() + 1,
          startDate.getMonth(),
          startDate.getDate(),
          23, 59, 59, 999
        );
        cancelDateMillis = cancelDate.getTime();
        console.log(`[Subscription Cancellation] Yearly subscription, end date calculated as: ${cancelDate.toISOString()}`);
      }
    }
    // For non-yearly subscriptions, use Stripe's current_period_end
    else if (subscription.current_period_end) {
      const cancelTimestamp = subscription.current_period_end;
      cancelDateMillis = cancelTimestamp * 1000;
      cancelDate = new Date(cancelDateMillis);
      console.log(`[Subscription Cancellation] Monthly subscription, using Stripe's end date: ${cancelDate.toISOString()}`);
    }
    // Fallback if no valid date can be determined
    else {
      // Fallback: Use 30 days from now as the cancellation date
      cancelDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      cancelDateMillis = cancelDate.getTime();
      console.log(`[Subscription Cancellation] No valid end date found, using fallback: ${cancelDate.toISOString()}`);
    }

    // We already have userData from above, just extract what we need
    const currentCredits = userData.credits || 0;
    const currentSubscriptionType = userData.subscriptionType;

    // Track subscription history
    let subscriptionHistory = [];
    if (userData.subscriptionHistory) {
      // Make a deep copy to avoid reference issues
      try {
        subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
      } catch (e) {
        console.error('Error parsing subscription history:', e);
        subscriptionHistory = [];
      }
    }

    if (currentSubscriptionType) {
      // Use a simple date string for the history to avoid Firestore issues
      const now = new Date();
      subscriptionHistory.push({
        type: currentSubscriptionType,
        endDate: now.toISOString(),
        cancelledEarly: true,
        credits: currentCredits
      });
    }

    // Update the user's document in Firestore
    const updateData = {
      subscriptionCancelRequested: true,
      subscriptionCancelRequestDate: admin.firestore.FieldValue.serverTimestamp(),
      subscriptionHistory: subscriptionHistory,
      // Ensure the subscription interval is set correctly
      subscriptionInterval: isYearlySubscription ? 'yearly' : 'monthly',
      // Note: We're not changing the credits - they will be preserved
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    // Only add the end date if we have a valid date
    if (cancelDate && !isNaN(cancelDate.getTime())) {
      updateData.subscriptionEndDate = admin.firestore.Timestamp.fromMillis(cancelDateMillis);
      // Also update the renewal date to match the end date
      updateData.subscriptionRenewalDate = admin.firestore.Timestamp.fromMillis(cancelDateMillis);
    }

    await admin.firestore().collection('Admins').doc(userId).update(updateData);

    console.log(`[Subscription Cancellation] Updated user ${userId} with cancellation details`);

    res.status(200).json({
      message: 'Subscription cancellation scheduled',
      cancelDate: cancelDate.toISOString(),
      status: 'success'
    });
  } catch (error) {
    console.error('[Subscription Cancellation] Error:', error);
    res.status(500).json({
      error: 'Failed to cancel subscription',
      details: error.message
    });
  }
});

// Endpoint to check if a user has used the free trial before
app.get('/check-free-trial-eligibility/:userId', async (req, res) => {
  const { userId } = req.params;
  console.log('[Free Trial Check] Checking eligibility for:', userId);

  if (!userId) {
    return res.status(400).json({ error: 'Missing user ID' });
  }

  try {
    const db = admin.firestore();
    const userDoc = await db.collection('Admins').doc(userId).get();

    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();
    const hasUsedFreeTrial = userData.hasUsedFreeTrial ||
                            (userData.subscriptionType === 'freeTrial' && userData.subscriptionEndDate) ||
                            userData.previousSubscriptions?.includes('freeTrial');

    console.log(`[Free Trial Check] User ${userId} eligibility:`, !hasUsedFreeTrial);

    res.status(200).json({
      eligible: !hasUsedFreeTrial,
      currentSubscription: userData.subscriptionType || null,
      subscriptionActive: userData.subscriptionActive || false
    });
  } catch (error) {
    console.error('[Free Trial Check] Error:', error);
    res.status(500).json({
      error: 'Failed to check free trial eligibility',
      details: error.message
    });
  }
});

// Stripe Checkout Session Route - Modified to support subscription plans
app.post('/create-checkout-session', async (req, res) => {
  const { priceId, userId, credits } = req.body;

  try {
    // Check if this is the free trial
    if (priceId === 'price_1REmsePqOZsaOO5kgjo38qgp') {
      // Check if user has already used the free trial
      const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
      if (!userDoc.exists) {
        return res.status(404).json({ error: 'User not found' });
      }

      const userData = userDoc.data();
      const hasUsedFreeTrial = userData.hasUsedFreeTrial ||
                              (userData.subscriptionType === 'freeTrial' && userData.subscriptionEndDate) ||
                              userData.previousSubscriptions?.includes('freeTrial');

      if (hasUsedFreeTrial) {
        return res.status(400).json({
          error: 'Free trial already used',
          message: 'You have already used your free trial. Please select a paid plan.'
        });
      }

      // Get the user's current data to preserve existing credits
      const existingCredits = userData.credits || 0;
      const currentSubscriptionType = userData.subscriptionType;

      // Add free trial credits to existing credits
      const totalCredits = existingCredits + 5; // 5 free trial credits
      console.log(`Adding 5 free trial credits to ${existingCredits} existing credits for a total of ${totalCredits}`);

      // Track subscription history
      let subscriptionHistory = [];
      if (userData.subscriptionHistory) {
        // Make a deep copy to avoid reference issues
        try {
          subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
        } catch (e) {
          console.error('Error parsing subscription history:', e);
          subscriptionHistory = [];
        }
      }

      if (currentSubscriptionType && currentSubscriptionType !== 'freeTrial') {
        subscriptionHistory.push({
          type: currentSubscriptionType,
          endDate: new Date().toISOString(), // Store as ISO string to avoid Firestore issues
          credits: existingCredits
        });
      }

      // Calculate free trial end date (exactly 14 days from now)
      const now = new Date();
      const freeTrialEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 14, 23, 59, 59, 999);
      console.log(`[Free Trial] Setting end date to ${freeTrialEndDate.toISOString()} (exactly 14 days from now)`);

      // For free trial, directly update the user's credits without going through Stripe
      await admin.firestore().collection('Admins').doc(userId).update({
        credits: totalCredits, // Add free trial credits to existing credits
        paid: false, // Keep as free user
        subscriptionActive: true,
        subscriptionType: 'freeTrial',
        hasUsedFreeTrial: true, // Mark that they've used their free trial
        subscriptionStartDate: admin.firestore.FieldValue.serverTimestamp(),
        subscriptionEndDate: admin.firestore.Timestamp.fromDate(freeTrialEndDate),
        subscriptionHistory: subscriptionHistory,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // Return a special response for free trial
      return res.json({
        isFree: true,
        redirectUrl: `${req.headers.origin}/success.html?free=true&userId=${userId}&credits=5&trialDays=14`
      });
    }

    // For paid subscriptions, create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      // Use a more SEO-friendly description for subscription plans
      subscription_data: {
        metadata: {
          userId: userId,
          credits: credits,
          plan: getPlanNameFromPriceId(priceId)
        }
      },
      allow_promotion_codes: true,
      success_url: `${req.headers.origin}/success.html?session_id={CHECKOUT_SESSION_ID}&userId=${userId}&credits=${credits}`,
      cancel_url: `${req.headers.origin}/cancel.html`,
      metadata: {
        userId: userId,
        credits: credits,
        plan: getPlanNameFromPriceId(priceId),
        immediateCharge: 'true' // Flag to indicate immediate charging was requested
      },
    });

    res.json({ id: session.id });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: error.message });
  }
});

// Top-up Checkout Session Route - For one-time purchases
app.post('/create-topup-checkout-session', async (req, res) => {
  const { priceId, userId, credits } = req.body;
  console.log('[Top-up] Creating checkout session:', { priceId, userId, credits });

  try {
    // Verify the user has an active subscription (not free trial)
    const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();
    if (!userData.subscriptionActive) {
      return res.status(400).json({
        error: 'No active subscription',
        message: 'You must have an active subscription to purchase top-ups.'
      });
    }

    if (userData.subscriptionType === 'freeTrial') {
      return res.status(400).json({
        error: 'Free trial users cannot purchase top-ups',
        message: 'Please upgrade to a paid subscription to purchase top-ups.'
      });
    }

    // For top-up purchases, create a one-time payment Stripe checkout session
    // Note: One-time payments are already processed immediately, but we'll add the flag for consistency
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'payment', // One-time payment
      allow_promotion_codes: true,
      success_url: `${req.headers.origin}/success.html?session_id={CHECKOUT_SESSION_ID}&userId=${userId}&credits=${credits}&topup=true`,
      cancel_url: `${req.headers.origin}/cancel.html`,
      metadata: {
        userId: userId,
        credits: credits,
        isTopUp: 'true',
        plan: getTopupNameFromPriceId(priceId),
        immediateCharge: 'true' // Flag to indicate immediate charging was requested
      },
      payment_intent_data: {
        capture_method: 'automatic' // Ensure immediate capture
      }
    });

    res.json({ id: session.id });
  } catch (error) {
    console.error('Error creating top-up checkout session:', error);
    res.status(500).json({ error: error.message });
  }
});

// Helper function to get plan name from price id
function getPlanNameFromPriceId(priceId) {
  // Test mode price IDs
  const testPriceMap = {
    // Yearly plans
    'price_1RNpW7PqOZsaOO5knOoIbBb0': 'Assess1',
    'price_1RMP2WPqOZsaOO5kEXSQnBS0': 'Assess100',
    'price_1RMP2ZPqOZsaOO5kpAcmM2Ur': 'Assess250',
    'price_1RMP2cPqOZsaOO5kOzAGzqc7': 'Assess500',
    // Free trial
    'price_1REmsePqOZsaOO5kgjo38qgp': 'Free Trial'
  };

  // Live mode price IDs
  const livePriceMap = {
    // Live subscription price IDs
    'price_1RNo7DL8F65CEkir2BjRHcAh': 'Assess1',
    'price_1RMjI6L8F65CEkirK8t2V3DG': 'Assess100',
    'price_1RMjI4L8F65CEkirnaFfwST4': 'Assess250',
    'price_1RMjI2L8F65CEkirqOJxuD42': 'Assess500'
  };

  // Select the appropriate map based on mode
  const priceMap = stripeMode === 'live' ? livePriceMap : testPriceMap;

  // Return the plan name or default
  return priceMap[priceId] || 'Custom Plan';
}

// Helper function to get plan details from price ID
function getPlanDetailsFromPriceId(priceId) {
  // Test mode price IDs
  const testPlanMap = {
    // Test price IDs
    'price_1RNpW7PqOZsaOO5knOoIbBb0': { name: 'Assess1', credits: 1 },
    'price_1RMP2WPqOZsaOO5kEXSQnBS0': { name: 'Assess100', credits: 100 },
    'price_1RMP2ZPqOZsaOO5kpAcmM2Ur': { name: 'Assess250', credits: 250 },
    'price_1RMP2cPqOZsaOO5kOzAGzqc7': { name: 'Assess500', credits: 500 },
    'price_1REmsePqOZsaOO5kgjo38qgp': { name: 'Free Trial', credits: 5 }
  };

  // Live mode price IDs
  const livePlanMap = {
    // Live subscription price IDs
    'price_1RNo7DL8F65CEkir2BjRHcAh': { name: 'Assess1', credits: 1 },
    'price_1RMjI6L8F65CEkirK8t2V3DG': { name: 'Assess100', credits: 100 },
    'price_1RMjI4L8F65CEkirnaFfwST4': { name: 'Assess250', credits: 250 },
    'price_1RMjI2L8F65CEkirqOJxuD42': { name: 'Assess500', credits: 500 }
  };

  // Select the appropriate map based on mode
  const planMap = stripeMode === 'live' ? livePlanMap : testPlanMap;

  return planMap[priceId] || null;
}

// Helper function to get top-up name from price id
function getTopupNameFromPriceId(priceId) {
  // Test mode price IDs
  const testTopupMap = {
    // Test top-up price IDs
    'price_1RMP2fPqOZsaOO5kk3cePRnn': 'TopUp100',
    'price_1RMP2hPqOZsaOO5k8YEHnxu4': 'TopUp250',
    'price_1RMP2kPqOZsaOO5kRLmLuPwu': 'TopUp500'
  };

  // Live mode price IDs
  const liveTopupMap = {
    // Live top-up price IDs
    'price_1RMjI0L8F65CEkirmgGw2jDw': 'TopUp100',
    'price_1RMjHuL8F65CEkirXNMZ4UnJ': 'TopUp250',
    'price_1RMjHpL8F65CEkirTkzlPtEB': 'TopUp500'
  };

  // Select the appropriate map based on mode
  const topupMap = stripeMode === 'live' ? liveTopupMap : testTopupMap;

  return topupMap[priceId] || 'Custom TopUp';
}

// Enhanced webhook handler for Stripe events
app.post('/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'];

  // Select the appropriate webhook secret based on mode
  const endpointSecret = stripeMode === 'live'
    ? process.env.STRIPE_LIVE_WEBHOOK_SECRET
    : process.env.STRIPE_TEST_WEBHOOK_SECRET;

  console.log(`Using ${stripeMode.toUpperCase()} mode webhook secret`);

  let event;

  try {
    // req.body is now a Buffer thanks to express.raw middleware
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    console.log('Webhook event constructed successfully:', event.type);
  } catch (err) {
    console.error(`Webhook Error: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Log the event type for debugging
  console.log(`Processing webhook event: ${event.type}`);

  // Handle checkout session completed events
  if (event.type === 'checkout.session.completed') {
    const session = event.data.object;
    console.log('Checkout session completed:', session.id);

    // Check if this was an immediate charge request
    const isImmediateCharge = session.metadata && session.metadata.immediateCharge === 'true';
    if (isImmediateCharge) {
      console.log(`Checkout session ${session.id} was configured for immediate charging`);
    }

    // Extract metadata
    const userId = session.metadata.userId;
    const credits = parseInt(session.metadata.credits);
    const planName = session.metadata.plan || 'Custom Plan';
    const isTopUp = session.metadata.isTopUp === 'true';

    console.log(`Updating user ${userId} with ${credits} credits for ${isTopUp ? 'top-up' : 'plan'} ${planName}`);

    try {
      // Get the user's current data
      const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
      if (!userDoc.exists) {
        console.error(`User ${userId} not found in Firestore`);
        return;
      }

      const userData = userDoc.data();
      const existingCredits = userData.credits || 0;
      const logPrefix = `[Checkout] [${session.id}]:`;

      // Handle differently based on whether this is a top-up or subscription
      if (isTopUp) {
        // For top-ups, we just add credits and extend the subscription end date
        console.log(`${logPrefix} Processing top-up purchase for user ${userId}`);

        // Get the current date (purchase date)
        const purchaseDate = new Date();
        console.log(`${logPrefix} Top-up purchase date: ${purchaseDate.toISOString()}`);

        // Get the current end date if it exists
        let currentEndDate = null;
        let isExtension = false;

        if (userData.subscriptionEndDate) {
          try {
            currentEndDate = userData.subscriptionEndDate.toDate ?
              userData.subscriptionEndDate.toDate() :
              new Date(userData.subscriptionEndDate);

            // Check if the current subscription is still active
            const now = new Date();
            if (currentEndDate > now) {
              isExtension = true;
              console.log(`${logPrefix} User has an active subscription ending on ${currentEndDate.toISOString()}`);
            } else {
              console.log(`${logPrefix} Previous subscription ended on ${currentEndDate.toISOString()}`);
            }
          } catch (dateError) {
            console.error(`${logPrefix} Error processing current end date:`, dateError);
            currentEndDate = null;
          }
        } else {
          console.log(`${logPrefix} No previous end date found`);
        }

        // For top-ups, always set the new end date to exactly 12 months from the purchase date
        const newEndDate = new Date(
          purchaseDate.getFullYear() + 1,
          purchaseDate.getMonth(),
          purchaseDate.getDate(),
          23, 59, 59, 999
        );
        console.log(`${logPrefix} Setting new end date to ${newEndDate.toISOString()} (12 months from purchase date)`);

        // Track top-up history
        let topUpHistory = [];
        if (userData.topUpHistory) {
          // Make a deep copy to avoid reference issues
          try {
            topUpHistory = JSON.parse(JSON.stringify(userData.topUpHistory));
          } catch (e) {
            console.error(`${logPrefix} Error parsing top-up history:`, e);
            topUpHistory = [];
          }
        }

        // Add this top-up to history with enhanced information
        topUpHistory.push({
          purchaseDate: purchaseDate.toISOString(),
          amount: credits,
          plan: planName,
          previousEndDate: currentEndDate ? currentEndDate.toISOString() : null,
          newEndDate: newEndDate.toISOString(),
          isExtension: isExtension,
          calculationMethod: '12_months_from_purchase_date',
          checkoutSessionId: session.id,
          paymentStatus: session.payment_status,
          totalAmount: session.amount_total / 100 // Convert from cents
        });

        // Additional data specific to top-ups
        const additionalData = {
          lastTopUpDate: admin.firestore.Timestamp.fromDate(purchaseDate),
          lastTopUpPurchaseDate: admin.firestore.Timestamp.fromDate(purchaseDate),
          lastTopUpAmount: credits,
          lastTopUpCheckoutSession: session.id,
          topUpHistory: topUpHistory
        };

        // Use our centralized function to award credits and update subscription
        const updateResult = await awardCreditsAndUpdateSubscription({
          userId,
          creditsToAdd: credits,
          currentCredits: existingCredits,
          planName: userData.subscriptionType || planName, // Keep the current plan name for top-ups
          endDate: newEndDate,
          isYearlySubscription: true, // Top-ups are always treated as yearly
          isRenewal: false,
          logPrefix,
          additionalData
        });

        if (!updateResult.success) {
          throw new Error(updateResult.error || 'Failed to update subscription with top-up');
        }

        console.log(`${logPrefix} Successfully updated user ${userId} with ${credits} top-up credits and extended subscription`);
      } else {
        // For subscriptions, we update all subscription details
        // Get subscription details to find renewal date
        const subscription = await stripe.subscriptions.retrieve(session.subscription);

        // Determine if this is a yearly subscription
        let isYearlySubscription = false;
        if (subscription && subscription.items && subscription.items.data && subscription.items.data.length > 0) {
          const item = subscription.items.data[0];
          if (item.plan && item.plan.interval === 'year') {
            isYearlySubscription = true;
            console.log(`${logPrefix} Detected yearly subscription plan`);
          }
        }

        // Log the subscription object for debugging
        console.log(`${logPrefix} Subscription details:`, {
          id: subscription.id,
          status: subscription.status,
          current_period_start: subscription.current_period_start,
          current_period_end: subscription.current_period_end,
          start_date: subscription.start_date,
          created: subscription.created
        });

        // Get the subscription start date (or use now as fallback)
        let startDate;
        if (subscription.start_date) {
          startDate = new Date(subscription.start_date * 1000);
          console.log(`${logPrefix} Using Stripe start_date: ${startDate.toISOString()}`);
        } else if (subscription.created) {
          startDate = new Date(subscription.created * 1000);
          console.log(`${logPrefix} Using Stripe created date: ${startDate.toISOString()}`);
        } else if (subscription.current_period_start) {
          startDate = new Date(subscription.current_period_start * 1000);
          console.log(`${logPrefix} Using Stripe current_period_start: ${startDate.toISOString()}`);
        } else {
          startDate = new Date();
          console.log(`${logPrefix} No valid start date found, using current date: ${startDate.toISOString()}`);
        }

        // Use our centralized function to calculate the end date
        const endDate = calculateSubscriptionEndDate({
          isYearlySubscription,
          startDate,
          stripeCurrentPeriodEnd: subscription?.current_period_end,
          isRenewal: false,
          logPrefix
        });

        // Track subscription history
        let subscriptionHistory = [];
        if (userData.subscriptionHistory) {
          // Make a deep copy to avoid reference issues
          try {
            subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
          } catch (e) {
            console.error(`${logPrefix} Error parsing subscription history:`, e);
            subscriptionHistory = [];
          }
        }

        const previousSubscriptionType = userData.subscriptionType;
        if (previousSubscriptionType && previousSubscriptionType !== planName) {
          subscriptionHistory.push({
            type: previousSubscriptionType,
            endDate: new Date().toISOString(), // Store as ISO string to avoid Firestore issues
            credits: existingCredits,
            replacedBy: planName,
            checkoutSessionId: session.id
          });
        }

        // Enhanced logging for the new plan
        subscriptionHistory.push({
          type: planName,
          action: 'subscription_created',
          startDate: new Date().toISOString(),
          endDate: endDate.toISOString(),
          credits: credits,
          checkoutSessionId: session.id,
          paymentStatus: session.payment_status
        });

        // Additional data specific to new subscriptions
        const additionalData = {
          checkoutSessionId: session.id
        };

        // Use our centralized function to award credits and update subscription
        const updateResult = await awardCreditsAndUpdateSubscription({
          userId,
          creditsToAdd: credits,
          currentCredits: existingCredits,
          planName,
          endDate,
          subscriptionId: session.subscription,
          isYearlySubscription,
          isRenewal: false,
          logPrefix,
          subscriptionHistory,
          additionalData
        });

        if (!updateResult.success) {
          throw new Error(updateResult.error || 'Failed to update subscription');
        }

        console.log(`${logPrefix} Successfully updated user ${userId} with ${credits} credits and subscription ${session.subscription}`);
      }
    } catch (error) {
      console.error(`Error updating Firestore for user ${userId}:`, error);
    }
  }

  // Handle subscription canceled event
  if (event.type === 'customer.subscription.deleted') {
    const subscription = event.data.object;
    console.log('Subscription deleted:', subscription.id);

    // Log the subscription object for debugging
    console.log('Subscription deletion details:', {
      id: subscription.id,
      status: subscription.status,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      start_date: subscription.start_date,
      created: subscription.created,
      cancel_at_period_end: subscription.cancel_at_period_end
    });

    try {
      // Find the admin with this subscription ID
      const snapshot = await admin.firestore()
        .collection('Admins')
        .where('subscriptionId', '==', subscription.id)
        .get();

      if (!snapshot.empty) {
        const adminDoc = snapshot.docs[0];
        console.log(`Found user ${adminDoc.id} with subscription ${subscription.id}`);

        // Get current user data to preserve credits and track history
        const userData = adminDoc.data();
        const currentCredits = userData.credits || 0;
        const currentSubscriptionType = userData.subscriptionType;
        const subscriptionInterval = userData.subscriptionInterval || 'monthly'; // Preserve the interval

        console.log(`User subscription details: type=${currentSubscriptionType}, interval=${subscriptionInterval}`);

        // Track subscription history
        let subscriptionHistory = [];
        if (userData.subscriptionHistory) {
          // Make a deep copy to avoid reference issues
          try {
            subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
          } catch (e) {
            console.error('Error parsing subscription history:', e);
            subscriptionHistory = [];
          }
        }

        if (currentSubscriptionType && currentSubscriptionType !== 'cancelled') {
          subscriptionHistory.push({
            type: currentSubscriptionType,
            interval: subscriptionInterval, // Add interval to history
            endDate: new Date().toISOString(), // Store as ISO string to avoid Firestore issues
            cancelled: true,
            credits: currentCredits
          });
        }

        // Update with subscription cancellation details
        await adminDoc.ref.update({
          paid: false,
          subscriptionId: null,
          subscriptionActive: false,
          subscriptionCancelledDate: admin.firestore.FieldValue.serverTimestamp(),
          subscriptionType: 'cancelled',
          // Keep the subscription interval for reference
          subscriptionInterval: subscriptionInterval,
          subscriptionHistory: subscriptionHistory,
          // Note: We're not changing the credits - they will be preserved
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log(`Successfully marked user ${adminDoc.id} subscription as canceled`);
      } else {
        console.log(`No user found with subscription ID ${subscription.id}`);
      }
    } catch (error) {
      console.error('Error handling subscription cancellation:', error);
    }
  }

  // Handle subscription updated event
  if (event.type === 'customer.subscription.updated') {
    const subscription = event.data.object;
    console.log('Subscription updated:', subscription.id);

    // Get the previous subscription state if available
    const previousAttributes = event.data.previous_attributes || {};
    console.log('Previous attributes:', previousAttributes);

    // Check if this is a reactivation (cancel_at_period_end changed from true to false)
    const isReactivation = previousAttributes.cancel_at_period_end === true &&
                          subscription.cancel_at_period_end === false;

    // Generate a unique webhook request ID for tracking
    const webhookRequestId = `webhook-reactivate-${subscription.id}-${Date.now()}`;

    if (isReactivation) {
      console.log(`Detected subscription reactivation for subscription ${subscription.id}`);
      console.log(`[Webhook] [${webhookRequestId}] Processing reactivation via webhook`);
    }

    try {
      // Find the admin with this subscription ID
      const snapshot = await admin.firestore()
        .collection('Admins')
        .where('subscriptionId', '==', subscription.id)
        .get();

      if (!snapshot.empty) {
        const adminDoc = snapshot.docs[0];
        console.log(`Found user ${adminDoc.id} with subscription ${subscription.id}`);

        // Get the subscription details
        let currentPeriodEnd;
        let hasValidEndDate = false;

        if (subscription && subscription.current_period_end) {
          try {
            // Check if this is a yearly subscription
            let isYearlySubscription = false;
            if (subscription && subscription.items && subscription.items.data && subscription.items.data.length > 0) {
              const item = subscription.items.data[0];
              if (item.plan && item.plan.interval === 'year') {
                isYearlySubscription = true;
                console.log('Subscription update - detected yearly subscription plan');
              }
            }

            // Get the user data to check for existing start date
            const userData = adminDoc.data();

            // Log the subscription object for debugging
            console.log('Subscription update details:', {
              id: subscription.id,
              status: subscription.status,
              current_period_start: subscription.current_period_start,
              current_period_end: subscription.current_period_end,
              start_date: subscription.start_date,
              created: subscription.created
            });

            // For yearly subscriptions, use the most reliable start date available
            if (isYearlySubscription) {
              let startDate;

              // Try to get start date from Firestore first (most reliable)
              if (userData.subscriptionStartDate) {
                startDate = userData.subscriptionStartDate.toDate ?
                  userData.subscriptionStartDate.toDate() :
                  new Date(userData.subscriptionStartDate);
                console.log(`Using existing subscription start date from Firestore: ${startDate.toISOString()}`);
              }
              // Try different Stripe fields as fallbacks
              else if (subscription.start_date) {
                startDate = new Date(subscription.start_date * 1000);
                console.log(`Using Stripe's start_date: ${startDate.toISOString()}`);
              }
              else if (subscription.created) {
                startDate = new Date(subscription.created * 1000);
                console.log(`Using Stripe's created date: ${startDate.toISOString()}`);
              }
              else if (subscription.current_period_start) {
                startDate = new Date(subscription.current_period_start * 1000);
                console.log(`Using Stripe's current_period_start: ${startDate.toISOString()}`);
              }
              else {
                // Last resort, use current date
                startDate = new Date();
                console.log(`No valid start date found, using current date: ${startDate.toISOString()}`);
              }

              // Set end date to exactly 1 year from start date
              currentPeriodEnd = new Date(
                startDate.getFullYear() + 1,
                startDate.getMonth(),
                startDate.getDate(),
                23, 59, 59, 999
              );
              console.log(`Yearly subscription - setting end date to: ${currentPeriodEnd.toISOString()}`);
              hasValidEndDate = true;
            }
            // For non-yearly subscriptions, use Stripe's current_period_end if available
            else if (subscription && subscription.current_period_end) {
              const endTimestamp = subscription.current_period_end;
              const endMillis = endTimestamp * 1000;
              currentPeriodEnd = new Date(endMillis);

              // Validate the date
              if (!isNaN(currentPeriodEnd.getTime())) {
                hasValidEndDate = true;
                console.log(`Non-yearly subscription - using Stripe's end date: ${currentPeriodEnd.toISOString()}`);
              } else {
                console.log(`Subscription update - invalid end date timestamp: ${endTimestamp}`);
                // Fallback to 30 days from now
                currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                console.log(`Subscription update - using fallback end date: ${currentPeriodEnd.toISOString()}`);
                hasValidEndDate = true;
              }
            }
            // Fallback if no current_period_end is available
            else {
              currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
              console.log(`No valid end date information found, using fallback: ${currentPeriodEnd.toISOString()}`);
              hasValidEndDate = true;
            }
          } catch (dateError) {
            console.error('Error processing subscription end date:', dateError);
            // Fallback to 30 days from now
            currentPeriodEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
            console.log(`Subscription update - using fallback end date after error: ${currentPeriodEnd.toISOString()}`);
            hasValidEndDate = true;
          }

          // Get metadata from subscription
          const metadata = subscription.metadata || {};

          // Get current user data
          const userData = adminDoc.data();
          const currentCredits = userData.credits || 0;
          const currentPlan = userData.subscriptionType;

          // Prepare update object
          const updateData = {
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            subscriptionInterval: isYearlySubscription ? 'yearly' : 'monthly' // Add subscription interval
          };

          // If this is a reactivation, handle it specially
          if (isReactivation) {
            console.log(`[Webhook] [${webhookRequestId}] Handling subscription reactivation in webhook`);

            // For reactivation, we want to keep the existing end date if it exists
            // and only update the cancellation status
            updateData.subscriptionCancelRequested = false;
            updateData.subscriptionCancelRequestDate = null;
            updateData.subscriptionActive = true;

            // Track reactivation in history
            let subscriptionHistory = [];
            if (userData.subscriptionHistory) {
              try {
                subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
              } catch (e) {
                console.error(`[Webhook] [${webhookRequestId}] Error parsing subscription history:`, e);
                subscriptionHistory = [];
              }
            }

            // Add reactivation entry to history
            subscriptionHistory.push({
              type: userData.subscriptionType,
              action: 'reactivated',
              date: new Date().toISOString(),
              source: 'webhook',
              requestId: webhookRequestId,
              previousEndDate: userData.subscriptionEndDate ?
                (userData.subscriptionEndDate.toDate ? userData.subscriptionEndDate.toDate().toISOString() : userData.subscriptionEndDate) : null
            });

            updateData.subscriptionHistory = subscriptionHistory;

            console.log(`[Webhook] [${webhookRequestId}] Reactivation update data:`, updateData);
          }
          // For normal updates, handle date fields
          else if (hasValidEndDate) {
            updateData.subscriptionEndDate = admin.firestore.Timestamp.fromMillis(currentPeriodEnd.getTime());
            updateData.subscriptionRenewalDate = admin.firestore.Timestamp.fromMillis(currentPeriodEnd.getTime());
          }

          // Check if this is a plan change by examining metadata and previous attributes

          // Check if there was a price change in this update
          const hasPriceChange = previousAttributes.plan &&
                                previousAttributes.plan.id !== subscription.plan.id;

          console.log(`Checking for price change:
            - Previous plan ID: ${previousAttributes.plan ? previousAttributes.plan.id : 'none'}
            - Current plan ID: ${subscription.plan ? subscription.plan.id : 'none'}
            - Price change detected: ${hasPriceChange}
          `);

          // Get the new plan details from metadata or from the subscription itself
          let newPlanCredits = null;
          let newPlanName = null;

          // First try to get from metadata
          if (metadata.credits) {
            newPlanCredits = parseInt(metadata.credits);
            newPlanName = metadata.plan;
            console.log(`Found plan details in metadata: ${newPlanName} with ${newPlanCredits} credits`);
          }
          // If not in metadata, try to get from the subscription plan
          else if (subscription.plan) {
            // Try to get plan details from the price ID
            const planDetails = getPlanDetailsFromPriceId(subscription.plan.id);
            if (planDetails) {
              newPlanCredits = planDetails.credits;
              newPlanName = planDetails.name;
              console.log(`Determined plan details from price ID: ${newPlanName} with ${newPlanCredits} credits`);
            }
            // If we still don't have plan details, try to get from plan metadata
            else if (subscription.plan.metadata) {
              newPlanCredits = subscription.plan.metadata.credits ?
                parseInt(subscription.plan.metadata.credits) : null;
              newPlanName = subscription.plan.metadata.plan;
              console.log(`Found plan details in plan metadata: ${newPlanName} with ${newPlanCredits} credits`);
            }
          }

          // If we still don't have credits, use a default based on the plan name
          if (newPlanName && !newPlanCredits) {
            if (newPlanName.includes('1') && newPlanName.includes('Assess1')) {
              newPlanCredits = 1;
            } else if (newPlanName.includes('100')) {
              newPlanCredits = 100;
            } else if (newPlanName.includes('250')) {
              newPlanCredits = 250;
            } else if (newPlanName.includes('500')) {
              newPlanCredits = 500;
            } else {
              // Default fallback
              newPlanCredits = 100;
            }
            console.log(`Using fallback credits based on plan name: ${newPlanCredits}`);
          }

          // Check if this is a scheduled change taking effect
          const isScheduledChange = metadata.scheduledChange === 'true';

          // Check if there's a pending change in the user data
          const hasPendingChange = userData.pendingSubscriptionChange != null;

          console.log(`Subscription update details:
            - Price change detected: ${hasPriceChange}
            - Scheduled change flag: ${isScheduledChange}
            - Pending change in database: ${hasPendingChange}
            - Current plan: ${currentPlan}
            - New plan from metadata/subscription: ${newPlanName}
          `);

          // If this is a plan change (either from metadata or detected from price change)
          if ((newPlanName && newPlanName !== currentPlan && newPlanCredits) ||
              (hasPriceChange && newPlanCredits)) {

            console.log(`Plan change detected: ${currentPlan} -> ${newPlanName} with ${newPlanCredits} new credits`);

            // Check if this is a scheduled change taking effect
            if (isScheduledChange) {
              console.log(`This is a scheduled change taking effect: ${metadata.originalPlan || currentPlan} -> ${metadata.newPlan || newPlanName}`);
            }

            // Track subscription history
            let subscriptionHistory = [];
            if (userData.subscriptionHistory) {
              // Make a deep copy to avoid reference issues
              try {
                subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
              } catch (e) {
                console.error('Error parsing subscription history:', e);
                subscriptionHistory = [];
              }
            }

            if (currentPlan) {
              subscriptionHistory.push({
                type: currentPlan,
                endDate: new Date().toISOString(), // Store as ISO string to avoid Firestore issues
                changed: true,
                scheduled: isScheduledChange,
                credits: currentCredits
              });
            }

            // Add new credits to existing credits
            const totalCredits = currentCredits + newPlanCredits;
            console.log(`Adding ${newPlanCredits} new credits to ${currentCredits} existing credits for a total of ${totalCredits}`);

            // Update with new plan details
            updateData.subscriptionType = newPlanName;
            updateData.credits = totalCredits;
            updateData.subscriptionHistory = subscriptionHistory;

            // If this was a scheduled change or there's a pending change in the database, remove the pending change flag
            if (isScheduledChange || hasPendingChange) {
              console.log('Removing pendingSubscriptionChange flag as the change has taken effect');
              updateData.pendingSubscriptionChange = admin.firestore.FieldValue.delete();
            }
          }
          // If there's a pending change in the database but no plan change detected,
          // we should still remove the pending change flag as it might be stale
          else if (hasPendingChange && hasPriceChange) {
            console.log('Price change detected but no plan details found. Removing pendingSubscriptionChange flag anyway.');
            updateData.pendingSubscriptionChange = admin.firestore.FieldValue.delete();

            // If we can determine the new plan name from the pending change
            if (userData.pendingSubscriptionChange && userData.pendingSubscriptionChange.toPlan) {
              const pendingPlanName = userData.pendingSubscriptionChange.toPlan;
              console.log(`Using plan name from pending change: ${pendingPlanName}`);

              // Determine credits based on the plan name
              let pendingPlanCredits = 0;
              if (pendingPlanName.includes('1') && pendingPlanName.includes('Assess1')) {
                pendingPlanCredits = 1;
              } else if (pendingPlanName.includes('100')) {
                pendingPlanCredits = 100;
              } else if (pendingPlanName.includes('250')) {
                pendingPlanCredits = 250;
              } else if (pendingPlanName.includes('500')) {
                pendingPlanCredits = 500;
              }

              if (pendingPlanCredits > 0) {
                console.log(`Determined credits from pending plan name: ${pendingPlanCredits}`);

                // Track subscription history
                let subscriptionHistory = [];
                if (userData.subscriptionHistory) {
                  try {
                    subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
                  } catch (e) {
                    console.error('Error parsing subscription history:', e);
                    subscriptionHistory = [];
                  }
                }

                // Add plan change entry to history
                subscriptionHistory.push({
                  type: pendingPlanName,
                  previousType: currentPlan,
                  action: 'scheduled_change',
                  date: new Date().toISOString(),
                  creditsAdded: pendingPlanCredits
                });

                // Add new credits to existing credits
                const totalCredits = currentCredits + pendingPlanCredits;
                console.log(`Adding ${pendingPlanCredits} new credits to ${currentCredits} existing credits for a total of ${totalCredits}`);

                // Update with new plan details
                updateData.subscriptionType = pendingPlanName;
                updateData.credits = totalCredits;
                updateData.subscriptionHistory = subscriptionHistory;
              }
            }
          }

          // Update the subscription details
          console.log(`${isReactivation ? `[Webhook] [${webhookRequestId}]` : ''} Updating Firestore document for user ${adminDoc.id}`);
          await adminDoc.ref.update(updateData);
        } else {
          console.log(`${isReactivation ? `[Webhook] [${webhookRequestId}]` : ''} Subscription update - no end date information in subscription`);
          // Just update the timestamp if we don't have any end date information
          await adminDoc.ref.update({
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });
        }
        console.log(`${isReactivation ? `[Webhook] [${webhookRequestId}]` : ''} Successfully updated subscription renewal for user ${adminDoc.id}`);
      }
    } catch (error) {
      console.error('Error handling subscription update:', error);
    }
  }

  // Handle invoice.payment_succeeded event for subscription renewals and immediate charges
  if (event.type === 'invoice.payment_succeeded') {
    const invoice = event.data.object;
    console.log(`INVOICE EVENT: Invoice payment succeeded (${invoice.id})`);

    // Log the entire invoice object for debugging
    console.log('Invoice details:', JSON.stringify(invoice, null, 2));

    // Check if this is a subscription renewal or an immediate charge
    if (invoice.billing_reason === 'subscription_cycle') {
      console.log(`RENEWAL EVENT: Detected subscription renewal from invoice: ${invoice.id}`);
      // Continue with renewal processing for subscription_cycle

      // Try to get the subscription ID from various locations in the invoice
      let subscriptionId = null;

      // Check direct subscription property
      if (invoice.subscription) {
        subscriptionId = invoice.subscription;
        console.log(`RENEWAL EVENT: Found subscription ID in invoice.subscription: ${subscriptionId}`);
      }
      // Check subscription property in lines data
      else if (invoice.lines && invoice.lines.data && invoice.lines.data.length > 0) {
        for (const line of invoice.lines.data) {
          if (line.subscription) {
            subscriptionId = line.subscription;
            console.log(`RENEWAL EVENT: Found subscription ID in invoice.lines.data[].subscription: ${subscriptionId}`);
            break;
          }
          // Check parent object for subscription details
          if (line.parent && line.parent.subscription_item_details && line.parent.subscription_item_details.subscription) {
            subscriptionId = line.parent.subscription_item_details.subscription;
            console.log(`RENEWAL EVENT: Found subscription ID in invoice.lines.data[].parent.subscription_item_details.subscription: ${subscriptionId}`);
            break;
          }
        }
      }
      // Check subscription_details property
      else if (invoice.subscription_details && invoice.subscription_details.id) {
        subscriptionId = invoice.subscription_details.id;
        console.log(`RENEWAL EVENT: Found subscription ID in invoice.subscription_details.id: ${subscriptionId}`);
      }
      // Check parent object at the invoice level
      else if (invoice.parent && invoice.parent.subscription_details && invoice.parent.subscription_details.subscription) {
        subscriptionId = invoice.parent.subscription_details.subscription;
        console.log(`RENEWAL EVENT: Found subscription ID in invoice.parent.subscription_details.subscription: ${subscriptionId}`);
      }
      // Check parent object with subscription_item_details at the invoice level
      else if (invoice.parent && invoice.parent.subscription_item_details && invoice.parent.subscription_item_details.subscription) {
        subscriptionId = invoice.parent.subscription_item_details.subscription;
        console.log(`RENEWAL EVENT: Found subscription ID in invoice.parent.subscription_item_details.subscription: ${subscriptionId}`);
      }
      // Check metadata for subscription ID
      else if (invoice.metadata && invoice.metadata.subscription_id) {
        subscriptionId = invoice.metadata.subscription_id;
        console.log(`RENEWAL EVENT: Found subscription ID in invoice.metadata.subscription_id: ${subscriptionId}`);
      }

      if (!subscriptionId) {
        console.error('RENEWAL EVENT: No subscription ID found in invoice after checking all possible locations');
        return res.status(200).json({received: true});
      }

      try {
        console.log(`RENEWAL EVENT: Processing renewal for subscription ID: ${subscriptionId}`);

        // Find the user with this subscription ID
        const snapshot = await admin.firestore()
          .collection('Admins')
          .where('subscriptionId', '==', subscriptionId)
          .get();

        if (snapshot.empty) {
          console.error(`RENEWAL EVENT: No user found with subscription ID ${subscriptionId}`);

          // Try to find the user by customer ID if available
          if (invoice.customer) {
            console.log(`RENEWAL EVENT: Attempting to find user by customer ID: ${invoice.customer}`);

            // First try the stripeCustomerId field
            let customerSnapshot = await admin.firestore()
              .collection('Admins')
              .where('stripeCustomerId', '==', invoice.customer)
              .get();

            // If not found, try the customerId field
            if (customerSnapshot.empty) {
              console.log(`RENEWAL EVENT: No user found with stripeCustomerId, trying customerId field`);
              customerSnapshot = await admin.firestore()
                .collection('Admins')
                .where('customerId', '==', invoice.customer)
                .get();
            }

            // If still not found, try the customer field
            if (customerSnapshot.empty) {
              console.log(`RENEWAL EVENT: No user found with customerId, trying customer field`);
              customerSnapshot = await admin.firestore()
                .collection('Admins')
                .where('customer', '==', invoice.customer)
                .get();
            }

            if (!customerSnapshot.empty) {
              const adminDoc = customerSnapshot.docs[0];
              console.log(`RENEWAL EVENT: Found user ${adminDoc.id} by customer ID ${invoice.customer}`);

              // Update the user's subscription ID if it's missing
              if (!adminDoc.data().subscriptionId) {
                await adminDoc.ref.update({
                  subscriptionId: subscriptionId,
                  stripeCustomerId: invoice.customer, // Ensure the customer ID is set
                  updatedAt: admin.firestore.FieldValue.serverTimestamp()
                });
                console.log(`RENEWAL EVENT: Updated user ${adminDoc.id} with missing subscription ID ${subscriptionId}`);
              }

              // Continue processing with this user document
              const renewalResult = await processRenewalForUser(adminDoc, subscriptionId, invoice);
              console.log(`RENEWAL EVENT: Renewal result:`, renewalResult);
              return res.status(200).json({received: true});
            } else {
              console.error(`RENEWAL EVENT: No user found with any customer ID field matching ${invoice.customer}`);

              // As a last resort, try to find by email if available in the invoice
              if (invoice.customer_email) {
                console.log(`RENEWAL EVENT: Attempting to find user by email: ${invoice.customer_email}`);
                const emailDoc = await admin.firestore().collection('Admins').doc(invoice.customer_email).get();

                if (emailDoc.exists) {
                  console.log(`RENEWAL EVENT: Found user by email: ${invoice.customer_email}`);

                  // Update the user with subscription and customer IDs
                  await emailDoc.ref.update({
                    subscriptionId: subscriptionId,
                    stripeCustomerId: invoice.customer,
                    updatedAt: admin.firestore.FieldValue.serverTimestamp()
                  });

                  // Process renewal
                  const renewalResult = await processRenewalForUser(emailDoc, subscriptionId, invoice);
                  console.log(`RENEWAL EVENT: Renewal result:`, renewalResult);
                  return res.status(200).json({received: true});
                } else {
                  console.error(`RENEWAL EVENT: No user found with email ${invoice.customer_email}`);
                }
              }

              return res.status(200).json({received: true});
            }
          } else {
            console.error(`RENEWAL EVENT: No customer ID available in the invoice`);
            return res.status(200).json({received: true});
          }
        }

        // If we found a user with the subscription ID, process the renewal
        const adminDoc = snapshot.docs[0];
        console.log(`RENEWAL EVENT: Found user ${adminDoc.id} for renewal processing`);
        const renewalResult = await processRenewalForUser(adminDoc, subscriptionId, invoice);
        console.log(`RENEWAL EVENT: Renewal result:`, renewalResult);
      } catch (error) {
        console.error('RENEWAL EVENT: Error handling subscription renewal:', error);
      }
    } else if (invoice.billing_reason === 'subscription_create') {
      console.log(`IMMEDIATE CHARGE EVENT: Detected immediate subscription charge from invoice: ${invoice.id}`);
      // For immediate charges, the checkout.session.completed event already handles the credits and subscription setup
      // So we don't need to do anything special here
    } else {
      console.log(`Invoice ${invoice.id} has billing_reason: ${invoice.billing_reason}, not processing as renewal`);
    }
  }

  // Always return a 200 response to acknowledge receipt of the webhook
  return res.status(200).json({received: true});
});

// Refactored and improved helper function to process subscription renewal for a user
async function processRenewalForUser(adminDoc, subscriptionId, invoice) {
  const renewalId = `renewal-${adminDoc.id}-${Date.now()}`;
  const logPrefix = `RENEWAL EVENT [${renewalId}]:`;
  console.log(`${logPrefix} Starting renewal process for user ${adminDoc.id}, subscription ${subscriptionId}`);

  try {
    // Get the user data
    const userData = adminDoc.data();
    if (!userData) {
      console.error(`${logPrefix} No user data found for ${adminDoc.id}`);
      return { success: false, error: 'No user data found' };
    }

    console.log(`${logPrefix} Current user data:`, {
      email: adminDoc.id,
      currentCredits: userData.credits || 0,
      currentPlan: userData.subscriptionType,
      currentEndDate: userData.subscriptionEndDate ?
        (userData.subscriptionEndDate.toDate ? userData.subscriptionEndDate.toDate().toISOString() : 'Invalid Date') : 'None'
    });

    // Get the current subscription details from Stripe
    console.log(`${logPrefix} Retrieving subscription details from Stripe`);
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Determine if this is a yearly subscription
    let isYearlySubscription = false;
    if (subscription && subscription.items && subscription.items.data && subscription.items.data.length > 0) {
      const item = subscription.items.data[0];
      if (item.plan && item.plan.interval === 'year') {
        isYearlySubscription = true;
        console.log(`${logPrefix} Detected yearly subscription plan from Stripe`);
      }
    }

    // Also check the user's data for subscription interval
    if (userData.subscriptionInterval === 'yearly') {
      isYearlySubscription = true;
      console.log(`${logPrefix} Confirmed yearly subscription from user data`);
    }

    // Get the current end date if it exists
    let currentEndDate = null;
    if (userData.subscriptionEndDate) {
      try {
        currentEndDate = userData.subscriptionEndDate.toDate ?
          userData.subscriptionEndDate.toDate() :
          new Date(userData.subscriptionEndDate);

        if (isNaN(currentEndDate.getTime())) {
          throw new Error('Invalid date');
        }

        console.log(`${logPrefix} Current subscription end date: ${currentEndDate.toISOString()}`);
      } catch (dateError) {
        console.error(`${logPrefix} Error processing current end date:`, dateError);
        currentEndDate = null;
      }
    }

    // Use our centralized function to calculate the new end date
    const newEndDate = calculateSubscriptionEndDate({
      isYearlySubscription,
      currentEndDate,
      stripeCurrentPeriodEnd: subscription?.current_period_end,
      isRenewal: true,
      logPrefix
    });

    // Get the plan details to determine credits to add
    let planCredits = 0;
    let planName = userData.subscriptionType || 'Unknown Plan';

    // First, check if this is a scheduled change that has taken effect by examining the invoice
    let isScheduledChange = false;
    let newPlanFromInvoice = null;

    console.log(`${logPrefix} Determining plan details and credits to add`);

    // Get plan details from invoice line items
    if (invoice && invoice.lines && invoice.lines.data && invoice.lines.data.length > 0) {
      const lineItem = invoice.lines.data[0];

      // Check if this is a scheduled change from metadata
      if (lineItem.metadata && lineItem.metadata.scheduledChange === 'true') {
        isScheduledChange = true;
        console.log(`${logPrefix} Detected scheduled change in invoice metadata`);

        if (lineItem.metadata.newPlan) {
          newPlanFromInvoice = lineItem.metadata.newPlan;
          console.log(`${logPrefix} New plan from metadata: ${newPlanFromInvoice}`);
        }
      }

      // Try to get plan details from price ID
      if (lineItem.price && lineItem.price.id) {
        const planDetails = getPlanDetailsFromPriceId(lineItem.price.id);
        if (planDetails) {
          planCredits = planDetails.credits;
          planName = planDetails.name;
          console.log(`${logPrefix} Determined plan from price ID ${lineItem.price.id}: ${planName} with ${planCredits} credits`);
        } else {
          console.log(`${logPrefix} Could not determine plan details from price ID ${lineItem.price.id}`);
        }
      } else if (lineItem.plan && lineItem.plan.id) {
        // Try plan ID if price ID isn't available
        const planDetails = getPlanDetailsFromPriceId(lineItem.plan.id);
        if (planDetails) {
          planCredits = planDetails.credits;
          planName = planDetails.name;
          console.log(`${logPrefix} Determined plan from plan ID ${lineItem.plan.id}: ${planName} with ${planCredits} credits`);
        }
      }

      // Check description as fallback
      if (planCredits === 0 && lineItem.description) {
        const description = lineItem.description;

        if (description.includes('Assess100')) {
          planName = 'Assess100';
          planCredits = 100;
        } else if (description.includes('Assess250')) {
          planName = 'Assess250';
          planCredits = 250;
        } else if (description.includes('Assess500')) {
          planName = 'Assess500';
          planCredits = 500;
        }

        if (planCredits > 0) {
          console.log(`${logPrefix} Determined plan from description: ${planName} with ${planCredits} credits`);
        }
      }
    }

    // If we still don't have plan details, try subscription directly
    if (planCredits === 0 && subscription) {
      // Check subscription items
      if (subscription.items && subscription.items.data && subscription.items.data.length > 0) {
        const item = subscription.items.data[0];
        if (item.price && item.price.id) {
          const planDetails = getPlanDetailsFromPriceId(item.price.id);
          if (planDetails) {
            planCredits = planDetails.credits;
            planName = planDetails.name;
            console.log(`${logPrefix} Determined plan from subscription item price ID: ${planName} with ${planCredits} credits`);
          }
        } else if (item.plan && item.plan.id) {
          const planDetails = getPlanDetailsFromPriceId(item.plan.id);
          if (planDetails) {
            planCredits = planDetails.credits;
            planName = planDetails.name;
            console.log(`${logPrefix} Determined plan from subscription item plan ID: ${planName} with ${planCredits} credits`);
          }
        }
      }

      // Check subscription metadata
      if (planCredits === 0 && subscription.metadata) {
        if (subscription.metadata.scheduledChange === 'true' && subscription.metadata.newPlan) {
          isScheduledChange = true;
          planName = subscription.metadata.newPlan;
          console.log(`${logPrefix} Using new plan from subscription metadata: ${planName}`);

          // Determine credits from plan name
          if (planName.includes('100')) {
            planCredits = 100;
          } else if (planName.includes('250')) {
            planCredits = 250;
          } else if (planName.includes('500')) {
            planCredits = 500;
          }
        } else if (subscription.metadata.credits) {
          planCredits = parseInt(subscription.metadata.credits);
          if (subscription.metadata.plan) {
            planName = subscription.metadata.plan;
          }
          console.log(`${logPrefix} Using plan details from subscription metadata: ${planName} with ${planCredits} credits`);
        }
      }
    }

    // Last resort: derive credits from plan name
    if (planCredits === 0) {
      if (planName.includes('1') && planName.includes('Assess1')) {
        planCredits = 1;
      } else if (planName.includes('100')) {
        planCredits = 100;
      } else if (planName.includes('250')) {
        planCredits = 250;
      } else if (planName.includes('500')) {
        planCredits = 500;
      } else {
        // Default fallback
        planCredits = 100;
      }
      console.log(`${logPrefix} Using fallback credits based on plan name: ${planCredits}`);
    }

    // Get current renewal count or initialize to 0
    const currentRenewalCount = userData.subscriptionRenewalCount || 0;
    const newRenewalCount = currentRenewalCount + 1;
    console.log(`${logPrefix} Incrementing renewal count from ${currentRenewalCount} to ${newRenewalCount}`);

    // Track subscription history
    let subscriptionHistory = [];
    if (userData.subscriptionHistory) {
      try {
        subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
      } catch (e) {
        console.error(`${logPrefix} Error parsing subscription history:`, e);
        subscriptionHistory = [];
      }
    }

    // Add renewal entry to history with detailed information
    subscriptionHistory.push({
      type: planName,
      action: 'renewed',
      date: new Date().toISOString(),
      renewalId: renewalId,
      previousEndDate: currentEndDate ? currentEndDate.toISOString() : null,
      newEndDate: newEndDate.toISOString(),
      renewalCount: newRenewalCount,
      creditsAdded: planCredits,
      invoiceId: invoice.id,
      invoiceTotal: invoice.total,
      paymentStatus: invoice.paid ? 'paid' : 'unpaid',
      scheduledChange: isScheduledChange,
      previousPlan: isScheduledChange ? userData.subscriptionType : null
    });

    // Additional data specific to renewals
    const additionalData = {
      subscriptionRenewalCount: newRenewalCount,
      subscriptionLastRenewalDate: admin.firestore.FieldValue.serverTimestamp(),
      lastRenewalInvoiceId: invoice.id,
      lastRenewalAmount: invoice.total / 100, // Convert from cents to currency
      lastRenewalId: renewalId
    };

    // If this is a scheduled change, handle any pending change flags
    if (isScheduledChange || newPlanFromInvoice) {
      console.log(`${logPrefix} Detected scheduled change taking effect. Updating subscription type to ${planName}`);

      // Remove pending change flag if it exists
      if (userData.pendingSubscriptionChange) {
        console.log(`${logPrefix} Removing pendingSubscriptionChange flag`);
        additionalData.pendingSubscriptionChange = admin.firestore.FieldValue.delete();
      }
    }

    // Use our centralized function to award credits and update subscription
    const updateResult = await awardCreditsAndUpdateSubscription({
      userId: adminDoc.id,
      creditsToAdd: planCredits,
      currentCredits: userData.credits || 0,
      planName,
      endDate: newEndDate,
      subscriptionId, // Pass the subscription ID
      isYearlySubscription,
      isRenewal: true,
      logPrefix,
      subscriptionHistory,
      additionalData
    });

    if (!updateResult.success) {
      throw new Error(updateResult.error || 'Failed to update subscription');
    }

    console.log(`${logPrefix} Successfully processed renewal for user ${adminDoc.id}. New end date: ${newEndDate.toISOString()}, New credits: ${updateResult.totalCredits}`);

    return {
      success: true,
      userId: adminDoc.id,
      renewalId: renewalId,
      renewalDetails: {
        plan: planName,
        creditsAdded: planCredits,
        newEndDate: newEndDate.toISOString(),
        wasScheduledChange: isScheduledChange,
        newTotalCredits: updateResult.totalCredits
      }
    };
  } catch (error) {
    console.error(`${logPrefix} Error handling subscription renewal:`, error);
    return {
      success: false,
      error: error.message,
      renewalId: renewalId,
      userId: adminDoc.id
    };
  }
}

// Endpoint to check and update expired free trials
app.post('/check-expired-trials', async (_req, res) => {
  try {
    const now = new Date();

    // Find all users with active free trials
    const snapshot = await admin.firestore()
      .collection('Admins')
      .where('subscriptionType', '==', 'freeTrial')
      .where('subscriptionActive', '==', true)
      .get();

    if (snapshot.empty) {
      return res.json({ message: 'No active free trials found', updated: 0 });
    }

    let updatedCount = 0;
    const updatePromises = [];

    snapshot.forEach(doc => {
      const userData = doc.data();

      if (userData.subscriptionEndDate) {
        const endDate = userData.subscriptionEndDate.toDate ?
          userData.subscriptionEndDate.toDate() :
          new Date(userData.subscriptionEndDate);

        // Check if the free trial has expired
        if (endDate < now) {
          console.log(`Free trial expired for user ${doc.id}, end date: ${endDate.toISOString()}`);

          // Update user status
          updatePromises.push(
            doc.ref.update({
              subscriptionActive: false,
              updatedAt: admin.firestore.FieldValue.serverTimestamp()
            })
          );

          updatedCount++;
        }
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    return res.json({
      message: `Updated ${updatedCount} expired free trials`,
      updated: updatedCount
    });
  } catch (error) {
    console.error('Error checking expired trials:', error);
    return res.status(500).json({ error: 'Failed to check expired trials' });
  }
});

// Endpoint to check and update all expired subscriptions
app.post('/check-expired-subscriptions', async (_req, res) => {
  try {
    const now = new Date();
    console.log(`Checking for expired subscriptions at ${now.toISOString()}`);

    // Find all users with active subscriptions
    const snapshot = await admin.firestore()
      .collection('Admins')
      .where('subscriptionActive', '==', true)
      .get();

    if (snapshot.empty) {
      return res.json({ message: 'No active subscriptions found', updated: 0 });
    }

    let updatedCount = 0;
    const updatePromises = [];

    snapshot.forEach(doc => {
      const userData = doc.data();

      if (userData.subscriptionEndDate) {
        const endDate = userData.subscriptionEndDate.toDate ?
          userData.subscriptionEndDate.toDate() :
          new Date(userData.subscriptionEndDate);

        // Check if the subscription has expired
        if (endDate < now) {
          console.log(`Subscription expired for user ${doc.id}, end date: ${endDate.toISOString()}`);

          // Track subscription history
          let subscriptionHistory = [];
          if (userData.subscriptionHistory) {
            try {
              subscriptionHistory = JSON.parse(JSON.stringify(userData.subscriptionHistory));
            } catch (e) {
              console.error('Error parsing subscription history:', e);
              subscriptionHistory = [];
            }
          }

          // Add the expired subscription to history
          if (userData.subscriptionType && userData.subscriptionType !== 'cancelled') {
            subscriptionHistory.push({
              type: userData.subscriptionType,
              interval: userData.subscriptionInterval || 'unknown',
              endDate: now.toISOString(),
              expired: true,
              credits: userData.credits || 0
            });
          }

          // Update user status and clear credits as they expire with the subscription
          updatePromises.push(
            doc.ref.update({
              subscriptionActive: false,
              subscriptionType: 'expired',
              subscriptionHistory: subscriptionHistory,
              credits: 0, // Clear credits when subscription expires
              updatedAt: admin.firestore.FieldValue.serverTimestamp()
            })
          );

          updatedCount++;
        }
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    return res.json({
      message: `Updated ${updatedCount} expired subscriptions`,
      updated: updatedCount
    });
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
    return res.status(500).json({ error: 'Failed to check expired subscriptions' });
  }
});

// Endpoint to schedule a subscription change (upgrade or downgrade)
app.post('/schedule-subscription-change', async (req, res) => {
  const { userId, subscriptionId, newPriceId, isUpgrade } = req.body;
  console.log('[Subscription Change] Starting scheduled change process for:', {
    userId,
    subscriptionId,
    newPriceId,
    isUpgrade
  });

  if (!userId || !subscriptionId || !newPriceId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    // First, retrieve the current subscription from Stripe
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ error: 'Subscription not found' });
    }

    // Determine the plan name based on the price ID
    const planName = getPlanNameFromPriceId(newPriceId);
    if (!planName) {
      return res.status(400).json({ error: 'Invalid price ID' });
    }

    // Get the user's current data
    const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();
    const currentPlan = userData.subscriptionType;

    // Schedule the subscription change in Stripe
    // For upgrades, we could apply immediately with proration
    // For downgrades, we schedule for the next billing cycle
    let updatedSubscription;

    if (isUpgrade) {
      // For upgrades, we could optionally apply immediately with proration
      // But for consistency, we'll schedule all changes for the next billing cycle
      updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
        items: [{
          id: subscription.items.data[0].id,
          price: newPriceId,
        }],
        proration_behavior: 'none',
        billing_cycle_anchor: 'unchanged', // Apply at next renewal
        metadata: {
          scheduledChange: 'true',
          originalPlan: currentPlan,
          newPlan: planName,
          userId: userId
        }
      });
    } else {
      // For downgrades, always schedule for the next billing cycle
      updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
        items: [{
          id: subscription.items.data[0].id,
          price: newPriceId,
        }],
        proration_behavior: 'none',
        billing_cycle_anchor: 'unchanged', // Apply at next renewal
        metadata: {
          scheduledChange: 'true',
          originalPlan: currentPlan,
          newPlan: planName,
          userId: userId
        }
      });
    }

    console.log(`[Subscription Change] Successfully scheduled ${isUpgrade ? 'upgrade' : 'downgrade'} for subscription ${subscriptionId}`);

    // Get the next billing date using the same logic as in the webhook handler
    let nextBillingDate;
    try {
      // Check if this is a yearly subscription
      let isYearlySubscription = false;
      if (updatedSubscription && updatedSubscription.items && updatedSubscription.items.data && updatedSubscription.items.data.length > 0) {
        const item = updatedSubscription.items.data[0];
        if (item.plan && item.plan.interval === 'year') {
          isYearlySubscription = true;
          console.log('[Subscription Change] Detected yearly subscription plan');
        }
      }

      // Also check the user's data for subscription interval
      if (userData.subscriptionInterval === 'yearly') {
        isYearlySubscription = true;
        console.log('[Subscription Change] Confirmed yearly subscription from user data');
      }

      // Log the subscription object for debugging
      console.log('[Subscription Change] Subscription details:', {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        current_period_start: updatedSubscription.current_period_start,
        current_period_end: updatedSubscription.current_period_end,
        start_date: updatedSubscription.start_date,
        created: updatedSubscription.created
      });

      // For yearly subscriptions, use the current subscription end date as the next billing date
      if (isYearlySubscription) {
        // First try to get the current subscription end date from Firestore
        if (userData.subscriptionEndDate) {
          const currentEndDate = userData.subscriptionEndDate.toDate ?
            userData.subscriptionEndDate.toDate() :
            new Date(userData.subscriptionEndDate);

          console.log(`[Subscription Change] Using current subscription end date from Firestore: ${currentEndDate.toISOString()}`);

          // The next billing date is the current end date
          nextBillingDate = new Date(currentEndDate);
          console.log(`[Subscription Change] Yearly subscription - setting next billing date to current end date: ${nextBillingDate.toISOString()}`);
        }
        // If no end date is available, fall back to calculating from start date
        else {
          // Get the subscription start date (or use now as fallback)
          let startDate;

          // Try to get start date from Firestore first (most reliable)
          if (userData.subscriptionStartDate) {
            startDate = userData.subscriptionStartDate.toDate ?
              userData.subscriptionStartDate.toDate() :
              new Date(userData.subscriptionStartDate);
            console.log(`[Subscription Change] Using existing subscription start date from Firestore: ${startDate.toISOString()}`);
          }
          // Try different Stripe fields as fallbacks
          else if (updatedSubscription.start_date) {
            startDate = new Date(updatedSubscription.start_date * 1000);
            console.log(`[Subscription Change] Using Stripe's start_date: ${startDate.toISOString()}`);
          }
          else if (updatedSubscription.created) {
            startDate = new Date(updatedSubscription.created * 1000);
            console.log(`[Subscription Change] Using Stripe's created date: ${startDate.toISOString()}`);
          }
          else if (updatedSubscription.current_period_start) {
            startDate = new Date(updatedSubscription.current_period_start * 1000);
            console.log(`[Subscription Change] Using Stripe's current_period_start: ${startDate.toISOString()}`);
          }
          else {
            // Last resort, use current date
            startDate = new Date();
            console.log(`[Subscription Change] No valid start date found, using current date: ${startDate.toISOString()}`);
          }

          // Set end date to exactly 1 year from start date (only as a fallback)
          nextBillingDate = new Date(
            startDate.getFullYear() + 1,
            startDate.getMonth(),
            startDate.getDate(),
            23, 59, 59, 999
          );
          console.log(`[Subscription Change] Yearly subscription - using fallback calculation: ${nextBillingDate.toISOString()}`);
        }
      }
      // For non-yearly subscriptions, use Stripe's current_period_end if available
      else if (updatedSubscription && updatedSubscription.current_period_end) {
        const endTimestamp = updatedSubscription.current_period_end;
        const endMillis = endTimestamp * 1000;
        nextBillingDate = new Date(endMillis);

        // Validate the date
        if (!isNaN(nextBillingDate.getTime())) {
          console.log(`[Subscription Change] Non-yearly subscription - using Stripe's end date: ${nextBillingDate.toISOString()}`);
        } else {
          console.log(`[Subscription Change] Invalid end date timestamp: ${endTimestamp}`);
          // Fallback to 30 days from now
          nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
          console.log(`[Subscription Change] Using fallback end date: ${nextBillingDate.toISOString()}`);
        }
      }
      // Fallback if no current_period_end is available
      else {
        nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
        console.log(`[Subscription Change] No valid end date information found, using fallback: ${nextBillingDate.toISOString()}`);
      }

      // Create a Firestore Timestamp from the date
      const scheduledDateTimestamp = admin.firestore.Timestamp.fromDate(nextBillingDate);

      // Update the user's document in Firestore to record the pending change
      await admin.firestore().collection('Admins').doc(userId).update({
        pendingSubscriptionChange: {
          type: isUpgrade ? 'upgrade' : 'downgrade',
          fromPlan: currentPlan,
          toPlan: planName,
          scheduledDate: scheduledDateTimestamp,
          priceId: newPriceId
        },
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`[Subscription Change] Successfully updated user document with pending change`);
    } catch (timestampError) {
      console.error('[Subscription Change] Error creating timestamp:', timestampError);

      // Still update the user's document but without the problematic timestamp
      await admin.firestore().collection('Admins').doc(userId).update({
        pendingSubscriptionChange: {
          type: isUpgrade ? 'upgrade' : 'downgrade',
          fromPlan: currentPlan,
          toPlan: planName,
          scheduledDateIso: nextBillingDate ? nextBillingDate.toISOString() : new Date().toISOString(),
          priceId: newPriceId
        },
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`[Subscription Change] Used ISO string date as fallback`);
    }

    return res.status(200).json({
      message: `Subscription ${isUpgrade ? 'upgrade' : 'downgrade'} scheduled successfully`,
      effectiveDate: nextBillingDate.toISOString(),
      fromPlan: currentPlan,
      toPlan: planName
    });
  } catch (error) {
    console.error('[Subscription Change] Error:', error);
    return res.status(500).json({
      error: `Failed to schedule subscription ${isUpgrade ? 'upgrade' : 'downgrade'}`,
      details: error.message
    });
  }
});

// Endpoint to cancel a pending subscription change
app.post('/cancel-subscription-change', async (req, res) => {
  const { userId, subscriptionId } = req.body;
  console.log('[Subscription Change] Cancelling pending change for:', { userId, subscriptionId });

  if (!userId || !subscriptionId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    // Get the user's current data
    const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();

    // Check if there's a pending change
    if (!userData.pendingSubscriptionChange) {
      return res.status(400).json({ error: 'No pending subscription change found' });
    }

    // Retrieve the current subscription from Stripe
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ error: 'Subscription not found' });
    }

    // Get the original price ID
    // We need to find the price ID for the original plan
    let originalPriceId;

    // Determine the original price ID based on the current plan and mode
    if (userData.subscriptionType === 'Assess1') {
      originalPriceId = stripeMode === 'live'
        ? 'price_1RNo7DL8F65CEkir2BjRHcAh'  // Live mode price ID
        : 'price_1RNpW7PqOZsaOO5knOoIbBb0';  // Test mode price ID
    } else if (userData.subscriptionType === 'Assess100') {
      originalPriceId = stripeMode === 'live'
        ? 'price_1RMjI6L8F65CEkirK8t2V3DG'  // Live mode price ID
        : 'price_1RMP2WPqOZsaOO5kEXSQnBS0';  // Test mode price ID
    } else if (userData.subscriptionType === 'Assess250') {
      originalPriceId = stripeMode === 'live'
        ? 'price_1RMjI4L8F65CEkirnaFfwST4'  // Live mode price ID
        : 'price_1RMP2ZPqOZsaOO5kpAcmM2Ur';  // Test mode price ID
    } else if (userData.subscriptionType === 'Assess500') {
      originalPriceId = stripeMode === 'live'
        ? 'price_1RMjI2L8F65CEkirqOJxuD42'  // Live mode price ID
        : 'price_1RMP2cPqOZsaOO5kOzAGzqc7';  // Test mode price ID
    } else {
      return res.status(400).json({ error: 'Cannot determine original price ID' });
    }

    // Revert the subscription change in Stripe
    await stripe.subscriptions.update(subscriptionId, {
      items: [{
        id: subscription.items.data[0].id,
        price: originalPriceId,
      }],
      proration_behavior: 'none',
      billing_cycle_anchor: 'unchanged',
      metadata: {
        scheduledChange: 'false'
      }
    });

    console.log(`[Subscription Change] Successfully cancelled pending change for subscription ${subscriptionId}`);

    // Update the user's document in Firestore to remove the pending change
    await admin.firestore().collection('Admins').doc(userId).update({
      pendingSubscriptionChange: admin.firestore.FieldValue.delete(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    return res.status(200).json({
      message: 'Pending subscription change cancelled successfully',
      currentPlan: userData.subscriptionType
    });
  } catch (error) {
    console.error('[Subscription Change] Error cancelling pending change:', error);
    return res.status(500).json({
      error: 'Failed to cancel pending subscription change',
      details: error.message
    });
  }
});

// Endpoint to fix subscription dates for existing users
app.post('/fix-subscription-dates', async (req, res) => {
  try {
    const { adminKey } = req.body;

    // Ensure only admins can run this
    if (adminKey !== process.env.ADMIN_SECRET_KEY) {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    // Get all active subscriptions
    const snapshot = await admin.firestore()
      .collection('Admins')
      .where('subscriptionActive', '==', true)
      .get();

    let updated = 0;
    let skipped = 0;
    const updatePromises = [];

    snapshot.forEach(doc => {
      const userData = doc.data();

      // Only process users with subscription IDs and end dates
      if (userData.subscriptionId && userData.subscriptionEndDate) {
        try {
          const endDate = userData.subscriptionEndDate.toDate();
          const now = new Date();

          // Check if there's a large gap suggesting yearly subscription
          const daysDiff = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));

          // If end date is more than 180 days away, likely yearly
          if (daysDiff > 180) {
            updatePromises.push(
              doc.ref.update({
                subscriptionInterval: 'yearly',
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
              })
            );
            updated++;
          } else {
            // Likely monthly subscription
            updatePromises.push(
              doc.ref.update({
                subscriptionInterval: 'monthly',
                updatedAt: admin.firestore.FieldValue.serverTimestamp()
              })
            );
            updated++;
          }
        } catch (error) {
          console.error(`Error processing user ${doc.id}:`, error);
          skipped++;
        }
      } else {
        skipped++;
      }
    });

    await Promise.all(updatePromises);

    return res.json({
      success: true,
      updated,
      skipped,
      total: snapshot.size
    });
  } catch (error) {
    console.error('Error fixing subscription dates:', error);
    return res.status(500).json({ error: 'Server error', details: error.message });
  }
});

// Endpoint to handle immediate upgrades from free trial to paid plans
app.post('/upgrade-from-free-trial', async (req, res) => {
  const { userId, priceId, credits } = req.body;
  console.log('[Free Trial Upgrade] Starting immediate upgrade process for:', {
    userId,
    priceId,
    credits
  });

  if (!userId || !priceId || !credits) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    // Get the user's current data
    const userDoc = await admin.firestore().collection('Admins').doc(userId).get();
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();

    // Verify user is on a free trial
    if (userData.subscriptionType !== 'freeTrial' &&
        userData.subscriptionType !== 'free trial' &&
        userData.subscriptionType !== 'Free Trial') {
      return res.status(400).json({
        error: 'User is not on a free trial',
        message: 'This endpoint is only for upgrading from a free trial to a paid plan'
      });
    }

    // Determine the plan name based on the price ID
    const planDetails = getPlanDetailsFromPriceId(priceId);
    if (!planDetails) {
      return res.status(400).json({ error: 'Invalid price ID' });
    }

    const planName = planDetails.name;
    console.log(`[Free Trial Upgrade] Upgrading from Free Trial to ${planName}`);

    // Create a new checkout session with immediate charging
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      subscription_data: {
        metadata: {
          userId: userId,
          credits: credits,
          plan: planName,
          upgradedFromFreeTrial: 'true'
        },
        payment_behavior: 'default_incomplete', // Attempt payment immediately
        trial_end: 'now' // Start billing immediately
      },
      allow_promotion_codes: true,
      success_url: `${req.headers.origin}/success.html?session_id={CHECKOUT_SESSION_ID}&userId=${userId}&credits=${credits}&upgradedFromFreeTrial=true`,
      cancel_url: `${req.headers.origin}/cancel.html`,
      metadata: {
        userId: userId,
        credits: credits,
        plan: planName,
        upgradedFromFreeTrial: 'true',
        immediateCharge: 'true' // Flag to indicate immediate charging was requested
      },
    });

    console.log(`[Free Trial Upgrade] Created checkout session: ${session.id}`);
    res.json({ id: session.id });
  } catch (error) {
    console.error('[Free Trial Upgrade] Error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Zoho CRM integration test endpoint
app.post('/test-zoho-crm', async (req, res) => {
  console.log('[Zoho CRM Test] Testing Zoho CRM integration');
  console.log('[Zoho CRM Test] Environment variables check:');
  console.log('[Zoho CRM Test] ZOHO_CLIENT_ID exists:', !!process.env.ZOHO_CLIENT_ID);
  console.log('[Zoho CRM Test] ZOHO_CLIENT_SECRET exists:', !!process.env.ZOHO_CLIENT_SECRET);
  console.log('[Zoho CRM Test] ZOHO_REFRESH_TOKEN exists:', !!process.env.ZOHO_REFRESH_TOKEN);
  console.log('[Zoho CRM Test] ZOHO_API_DOMAIN:', process.env.ZOHO_API_DOMAIN || 'Not set (using default)');
  console.log('[Zoho CRM Test] ZOHO_ACCOUNTS_URL:', process.env.ZOHO_ACCOUNTS_URL || 'Not set (using default)');

  try {
    // Use provided test data or default test data
    const testData = req.body || null;
    console.log('[Zoho CRM Test] Using test data:', testData || 'Default test data');

    // Run the test
    const result = await zohoCRM.testIntegration(testData);

    // Return the result
    res.status(result.success ? 200 : 500).json(result);
  } catch (error) {
    console.error('[Zoho CRM Test] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Zoho CRM integration test failed',
      error: error.message,
      stack: process.env.NODE_ENV === 'production' ? undefined : error.stack
    });
  }
});

// Endpoint to capture lead source with IP address
app.post('/track-lead-source', async (req, res) => {
  try {
    const { email, leadSource, campaignData, userAgent } = req.body;
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
    
    console.log('[Lead Tracking] Capturing lead source:', {
      email,
      leadSource,
      ip: clientIp
    });

    const db = admin.firestore();
    
    // Update the lead source document with IP address
    const leadSourceQuery = await db.collection('leadSources')
      .where('email', '==', email)
      .orderBy('signupDate', 'desc')
      .limit(1)
      .get();

    if (!leadSourceQuery.empty) {
      const leadDoc = leadSourceQuery.docs[0];
      await leadDoc.ref.update({
        ipAddress: clientIp,
        serverTimestamp: admin.firestore.FieldValue.serverTimestamp()
      });
    }

    res.status(200).json({
      success: true,
      message: 'Lead source tracked successfully'
    });
  } catch (error) {
    console.error('[Lead Tracking] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track lead source',
      error: error.message
    });
  }
});


// Endpoint to add a new user to Zoho CRM
app.post('/add-to-zoho-crm', async (req, res) => {
  console.log('[Zoho CRM] Adding user to Zoho CRM');

  try {
    const { firstname, lastname, email, company } = req.body;

    // Validate required fields
    if (!firstname || !lastname || !email || !company) {
      return res.status(400).json({
        success: false,
        message: 'Missing required user data fields'
      });
    }

    // Add the user to Zoho CRM
    const result = await zohoCRM.addLead({
      firstname,
      lastname,
      email,
      company
    });

    console.log('[Zoho CRM] User added successfully:', email);
    res.status(200).json({
      success: true,
      message: 'User added to Zoho CRM successfully',
      result
    });
  } catch (error) {
    console.error('[Zoho CRM] Error adding user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add user to Zoho CRM',
      error: error.message
    });
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
