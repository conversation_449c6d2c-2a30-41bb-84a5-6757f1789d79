/**
 * Zoho CRM Integration Module
 *
 * This module handles the integration with Zoho CRM API to send user signup data.
 */

const axios = require('axios');
require('dotenv').config();

// Zoho CRM API endpoints - support multiple domains based on region
const ZOHO_ACCOUNTS_URL = process.env.ZOHO_ACCOUNTS_URL || 'https://accounts.zoho.com/oauth/v2/token';

// Different API domains for different regions
const ZOHO_API_DOMAINS = {
  US: 'https://www.zohoapis.com',
  EU: 'https://www.zohoapis.eu',
  IN: 'https://www.zohoapis.in',
  AU: 'https://www.zohoapis.com.au',
  JP: 'https://www.zohoapis.jp',
  CN: 'https://www.zohoapis.com.cn'
};

// Get the API domain from environment variable or default to US
const ZOHO_API_DOMAIN = process.env.ZOHO_API_DOMAIN || ZOHO_API_DOMAINS.US;
const ZOHO_API_URL = `${ZOHO_API_DOMAIN}/crm/v2/Leads`;

// Cache for the access token to avoid unnecessary API calls
let accessTokenCache = {
  token: null,
  expiresAt: null
};

/**
 * Get an access token for Zoho CRM API
 * @returns {Promise<string>} The access token
 */
// Track rate limiting
let lastTokenRequest = 0;
const MIN_REQUEST_INTERVAL = 60000; // 1 minute in milliseconds

async function getAccessToken() {
  // Check if we have a valid cached token
  const now = Date.now();
  if (accessTokenCache.token && accessTokenCache.expiresAt && now < accessTokenCache.expiresAt) {
    console.log('[Zoho CRM] Using cached access token');
    return accessTokenCache.token;
  }

  try {
    // Check if we need to wait due to rate limiting
    const timeSinceLastRequest = now - lastTokenRequest;
    if (lastTokenRequest > 0 && timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
      const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
      console.log(`[Zoho CRM] Rate limiting: Waiting ${waitTime}ms before requesting a new token`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    // Update the last request timestamp
    lastTokenRequest = Date.now();

    console.log('[Zoho CRM] Generating new access token');

    // Get refresh token from environment variables
    const refreshToken = process.env.ZOHO_REFRESH_TOKEN;
    const clientId = process.env.ZOHO_CLIENT_ID;
    const clientSecret = process.env.ZOHO_CLIENT_SECRET;

    if (!refreshToken || !clientId || !clientSecret) {
      throw new Error('Missing Zoho CRM credentials in environment variables');
    }

    // Log the request details (without sensitive info)
    console.log(`[Zoho CRM] Requesting access token from: ${ZOHO_ACCOUNTS_URL}`);
    console.log(`[Zoho CRM] Using client ID: ${clientId.substring(0, 8)}...`);
    console.log(`[Zoho CRM] Using refresh token: ${refreshToken.substring(0, 8)}...`);

    // Request a new access token using the refresh token
    const response = await axios({
      url: ZOHO_ACCOUNTS_URL,
      method: 'post',
      params: {
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
        client_id: clientId,
        client_secret: clientSecret,
      },
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    // Log the response (without the actual token)
    console.log('[Zoho CRM] Access token response received');
    console.log('[Zoho CRM] Response status:', response.status);
    console.log('[Zoho CRM] Response data:', JSON.stringify(response.data, null, 2));

    if (response.data.error) {
      throw new Error(`Zoho API error: ${response.data.error} - ${response.data.error_description || 'No description provided'}`);
    }

    if (!response.data.access_token) {
      throw new Error('Access token not found in response');
    }

    // Cache the token with expiration time (subtract 5 minutes to be safe)
    const expiresInMs = (response.data.expires_in - 300) * 1000;
    accessTokenCache = {
      token: response.data.access_token,
      expiresAt: now + expiresInMs
    };

    console.log(`[Zoho CRM] Access token generated successfully, expires in ${response.data.expires_in} seconds`);

    return response.data.access_token;
  } catch (error) {
    console.error('[Zoho CRM] Error getting access token:', error.message);

    // Check for rate limiting errors
    if (error.response) {
      console.error('[Zoho CRM] Error response status:', error.response.status);
      console.error('[Zoho CRM] Error response data:', JSON.stringify(error.response.data, null, 2));

      // Check for rate limiting error messages
      const errorData = error.response.data;
      if (
        (errorData.error === 'Access Denied' &&
         errorData.error_description &&
         errorData.error_description.includes('too many requests')) ||
        errorData.status === 'failure'
      ) {
        console.log('[Zoho CRM] Rate limit exceeded. Waiting 2 minutes before retrying...');

        // Update the last request timestamp to enforce waiting
        lastTokenRequest = Date.now();

        // Wait for 2 minutes and then try again
        await new Promise(resolve => setTimeout(resolve, 120000)); // 2 minutes
        return getAccessToken(); // Recursive call after waiting
      }
    } else if (error.message.includes('Zoho API error')) {
      // This is an error we've already formatted from the response data
      throw error;
    }

    throw new Error(`Failed to get Zoho CRM access token: ${error.message}`);
  }
}

/**
 * Add a new lead to Zoho CRM
 * @param {Object} userData - User data to add as a lead
 * @param {string} userData.firstname - User's first name
 * @param {string} userData.lastname - User's last name
 * @param {string} userData.email - User's email
 * @param {string} userData.company - User's company name
 * @returns {Promise<Object>} The response from Zoho CRM
 */
async function addLead(userData) {
  try {
    // Validate required fields
    if (!userData.firstname || !userData.lastname || !userData.email || !userData.company) {
      throw new Error('Missing required user data fields');
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Prepare the lead data with more comprehensive fields
    const leadData = {
      data: [{
        Company: userData.company,
        Last_Name: userData.lastname,
        First_Name: userData.firstname,
        Email: userData.email,
        Lead_Source: 'Skills Assess Dashboard',
        Lead_Status: 'Not Contacted',
        // Remove Source field and use Lead_Source instead
        // Add Phone field as empty string if not provided (some Zoho setups require it)
        Phone: userData.phone || '',
        // Add Industry if available
        Industry: userData.industry || 'Education/Training'
      }],
      trigger: [
        'approval',
        'workflow',
        'blueprint',
      ],
    };

    // Log the request details
    console.log(`[Zoho CRM] Sending lead data to: ${ZOHO_API_URL}`);
    console.log('[Zoho CRM] Lead data:', JSON.stringify(leadData, null, 2));
    console.log('[Zoho CRM] Using access token:', accessToken.substring(0, 10) + '...');

    // Try different API endpoints and authorization formats
    let response;
    let error;

    // Define the headers
    const headers = {
      'Content-Type': 'application/json'
    };

    // Try the standard format first
    headers.Authorization = `Zoho-oauthtoken ${accessToken}`;
    console.log('[Zoho CRM] Using authorization header:', headers.Authorization.substring(0, 20) + '...');

    try {
      // First attempt with standard format
      response = await axios.post(
        ZOHO_API_URL,
        leadData,
        { headers }
      );
      // If successful, return the response
      console.log('[Zoho CRM] First attempt successful');
    } catch (err) {
      console.log('[Zoho CRM] First attempt failed:', err.message);
      error = err;

      // Try with a different authorization format
      try {
        headers.Authorization = `Bearer ${accessToken}`;
        console.log('[Zoho CRM] Trying with Bearer token format');

        response = await axios.post(
          ZOHO_API_URL,
          leadData,
          { headers }
        );
        console.log('[Zoho CRM] Bearer token format successful');
      } catch (err2) {
        console.log('[Zoho CRM] Bearer token format failed:', err2.message);

        // Try with a different API endpoint format
        try {
          // Try with a different API endpoint (v3 instead of v2)
          const alternativeUrl = ZOHO_API_URL.replace('/v2/', '/v3/');
          console.log(`[Zoho CRM] Trying alternative API endpoint: ${alternativeUrl}`);

          headers.Authorization = `Zoho-oauthtoken ${accessToken}`;
          response = await axios.post(
            alternativeUrl,
            leadData,
            { headers }
          );
          console.log('[Zoho CRM] Alternative API endpoint successful');
        } catch (err3) {
          // If all attempts fail, throw the original error
          console.log('[Zoho CRM] All attempts failed');
          throw error;
        }
      }
    }

    console.log('[Zoho CRM] Response status:', response.status);
    console.log('[Zoho CRM] Lead added successfully:', response.data);

    // Check if the response indicates an error even with 200/202 status
    if (response.data && response.data.data && response.data.data[0] && response.data.data[0].status === 'error') {
      console.error('[Zoho CRM] API returned error in response:', response.data.data[0]);
      throw new Error(`Zoho CRM API Error: ${response.data.data[0].message} - ${response.data.data[0].code}`);
    }

    return response.data;
  } catch (error) {
    console.error('[Zoho CRM] Error adding lead:', error.message);
    if (error.response) {
      console.error('[Zoho CRM] Response status:', error.response.status);
      console.error('[Zoho CRM] Response data:', JSON.stringify(error.response.data, null, 2));

      // If it's an INVALID_DATA error, provide more details
      if (error.response.data && error.response.data.data && error.response.data.data[0]) {
        const errorData = error.response.data.data[0];
        if (errorData.code === 'INVALID_DATA' && errorData.details) {
          console.error('[Zoho CRM] Invalid data details:', JSON.stringify(errorData.details, null, 2));
        }
      }
    }
    throw new Error(`Failed to add lead to Zoho CRM: ${error.message}`);
  }
}

/**
 * Test the Zoho CRM integration
 * @param {Object} userData - Test user data
 * @returns {Promise<Object>} The test results
 */
async function testIntegration(userData = null) {
  try {
    // Use test data if none provided
    const testData = userData || {
      firstname: 'Test',
      lastname: 'User',
      email: '<EMAIL>',
      company: 'Test Company'
    };

    // Test getting an access token
    const accessToken = await getAccessToken();
    console.log('[Zoho CRM Test] Successfully obtained access token');

    // Test adding a lead with test data
    const result = await addLead(testData);

    return {
      success: true,
      message: 'Zoho CRM integration test successful',
      accessToken: accessToken ? 'Valid token obtained' : 'Failed to get token',
      leadResult: result
    };
  } catch (error) {
    console.error('[Zoho CRM Test] Integration test failed:', error.message);
    return {
      success: false,
      message: `Zoho CRM integration test failed: ${error.message}`,
      error: error.message
    };
  }
}

module.exports = {
  addLead,
  testIntegration
};
